# دليل إصلاح مشاكل البناء - Work Space Manager

## المشكلة الحالية
```
error NU1102: Unable to find package Microsoft.Toolkit.Win32.UI.Controls with version (>= 6.1.3)
```

## الحلول المتاحة

### الحل الأول: استخدام البناء المبسط (الموصى به)

1. **تشغيل البناء المبسط**
   ```bash
   build-simple.bat
   ```
   
   هذا الملف سيستخدم إصدار مبسط من المشروع بدون الحزم المعقدة.

### الحل الثاني: إصلاح المراجع يدوياً

1. **تشغيل ملف الإصلاح**
   ```bash
   fix-build.bat
   ```

2. **أو تشغيل الأوامر يدوياً**
   ```bash
   dotnet clean
   dotnet restore
   dotnet build --configuration Release
   ```

### الحل الثالث: استخدام Visual Studio

1. فتح المشروع في Visual Studio
2. إزالة الحزم المشكلة من NuGet Package Manager
3. إعادة بناء المشروع

## التغييرات المطلوبة

### 1. تحديث ملف المشروع
استبدال محتوى `WorkSpaceManager.csproj` بـ:

```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.Sqlite" Version="6.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
  </ItemGroup>
</Project>
```

### 2. إصلاح مراجع SQLite
تغيير جميع المراجع من:
```csharp
using System.Data.SQLite;
SQLiteConnection
SQLiteCommand
```

إلى:
```csharp
using Microsoft.Data.Sqlite;
SqliteConnection
SqliteCommand
```

### 3. إزالة Material Design (مؤقتاً)
إذا استمرت المشاكل، يمكن إزالة Material Design واستخدام WPF العادي.

## خطوات الإصلاح السريع

### الخطوة 1: نسخ احتياطي
```bash
copy WorkSpaceManager.csproj WorkSpaceManager.csproj.backup
```

### الخطوة 2: استخدام الإصدار المبسط
```bash
copy WorkSpaceManager-Simple.csproj WorkSpaceManager.csproj
```

### الخطوة 3: تنظيف وبناء
```bash
dotnet clean
dotnet restore
dotnet build
```

### الخطوة 4: اختبار التشغيل
```bash
dotnet run
```

## إصلاح ملفات الخدمات

إذا ظهرت أخطاء في ملفات الخدمات، قم بتشغيل:

```bash
powershell -ExecutionPolicy Bypass -File "fix-sqlite-references.ps1"
```

أو قم بالتغيير يدوياً في كل ملف:

### قبل:
```csharp
using System.Data.SQLite;
using var connection = new SQLiteConnection(connectionString);
using var command = new SQLiteCommand(query, connection);
```

### بعد:
```csharp
using Microsoft.Data.Sqlite;
using var connection = new SqliteConnection(connectionString);
using var command = new SqliteCommand(query, connection);
```

## الملفات المتأثرة

يجب تحديث هذه الملفات:
- `Data/DatabaseService.cs`
- `Services/CustomerService.cs`
- `Services/SessionService.cs`
- `Services/ProductService.cs`
- `Services/PurchaseService.cs`
- `Services/ShiftService.cs`
- `Services/ReportService.cs`

## اختبار النجاح

بعد الإصلاح، يجب أن تحصل على:
```
Build succeeded.
    0 Warning(s)
    0 Error(s)
```

## إذا استمرت المشاكل

### الحل الأخير: مشروع جديد
1. إنشاء مشروع WPF جديد
2. نسخ ملفات الكود فقط
3. إضافة الحزم واحدة تلو الأخرى

### طلب المساعدة
إذا استمرت المشاكل، يرجى إرسال:
- رسالة الخطأ الكاملة
- محتوى ملف `.csproj`
- إصدار .NET المثبت (`dotnet --version`)

## نصائح إضافية

1. **تأكد من إصدار .NET**
   ```bash
   dotnet --version
   ```
   يجب أن يكون 6.0 أو أحدث

2. **تنظيف الكاش**
   ```bash
   dotnet nuget locals all --clear
   ```

3. **إعادة تشغيل الكمبيوتر**
   أحياناً يحل مشاكل الذاكرة المؤقتة

---
**ملاحظة**: الإصدار المبسط سيعمل بدون Material Design، ولكن جميع الوظائف الأساسية ستعمل بشكل طبيعي.
