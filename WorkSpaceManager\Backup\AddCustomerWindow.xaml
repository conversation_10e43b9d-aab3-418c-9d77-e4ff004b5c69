<Window x:Class="WorkSpaceManager.Views.AddCustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة عميل جديد" 
        Height="450" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="إضافة عميل جديد" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"
                   HorizontalAlignment="Center"/>

        <!-- النموذج -->
        <StackPanel Grid.Row="1" Margin="0,0,0,20">
            
            <!-- اسم العميل -->
            <TextBlock Text="اسم العميل: *" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtCustomerName" 
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"/>

            <!-- رقم الهاتف -->
            <TextBlock Text="رقم الهاتف:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtPhone" 
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"/>

            <!-- البريد الإلكتروني -->
            <TextBlock Text="البريد الإلكتروني:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtEmail" 
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"/>

            <!-- ملاحظات -->
            <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtNotes" 
                     Height="80"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"/>

        </StackPanel>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            
            <Button x:Name="BtnSave" 
                    Content="حفظ" 
                    Background="#4CAF50" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    Click="BtnSave_Click"/>
            
            <Button x:Name="BtnCancel" 
                    Content="إلغاء" 
                    Background="#F44336" 
                    Foreground="White" 
                    Padding="20,10"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
