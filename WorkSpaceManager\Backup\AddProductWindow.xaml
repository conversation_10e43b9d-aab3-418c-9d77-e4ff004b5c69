<Window x:Class="WorkSpaceManager.Views.AddProductWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة منتج جديد" 
        Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="إضافة منتج جديد" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"
                   HorizontalAlignment="Center"/>

        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,0,0,20">
                
                <!-- اسم المنتج -->
                <TextBlock Text="اسم المنتج: *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtProductName" 
                         Margin="0,0,0,15"
                         Height="35"
                         VerticalContentAlignment="Center"/>

                <!-- الفئة -->
                <TextBlock Text="الفئة:" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CmbCategory" 
                          IsEditable="True"
                          Margin="0,0,0,15"
                          Height="35"/>

                <!-- السعر -->
                <TextBlock Text="السعر (ريال): *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtPrice" 
                         Text="0.00"
                         Margin="0,0,0,15"
                         Height="35"
                         VerticalContentAlignment="Center"/>

                <!-- التكلفة -->
                <TextBlock Text="التكلفة (ريال):" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtCost" 
                         Text="0.00"
                         Margin="0,0,0,15"
                         Height="35"
                         VerticalContentAlignment="Center"/>

                <!-- الكمية -->
                <TextBlock Text="الكمية الأولية:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtQuantity" 
                         Text="0"
                         Margin="0,0,0,15"
                         Height="35"
                         VerticalContentAlignment="Center"/>

                <!-- الباركود -->
                <TextBlock Text="الباركود:" FontWeight="Bold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="TxtBarcode" 
                             Grid.Column="0"
                             Height="35"
                             VerticalContentAlignment="Center"/>
                    
                    <Button Grid.Column="1" 
                            Content="🎲 توليد"
                            Margin="10,0,0,0"
                            Padding="10,5"
                            Background="#9E9E9E"
                            Foreground="White"
                            Click="BtnGenerateBarcode_Click"/>
                </Grid>

                <!-- حالة المنتج -->
                <CheckBox x:Name="ChkIsActive" 
                          Content="المنتج نشط" 
                          FontWeight="Bold"
                          IsChecked="True"
                          Margin="0,0,0,15"/>

                <!-- الوصف -->
                <TextBlock Text="الوصف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtDescription" 
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"/>

            </StackPanel>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            
            <Button x:Name="BtnSave" 
                    Content="💾 حفظ المنتج" 
                    Background="#4CAF50" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    Click="BtnSave_Click"/>
            
            <Button x:Name="BtnCancel" 
                    Content="إلغاء" 
                    Background="#F44336" 
                    Foreground="White" 
                    Padding="20,10"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
