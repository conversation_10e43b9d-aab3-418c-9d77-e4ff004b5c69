<Page x:Class="WorkSpaceManager.Views.CustomersPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:local="clr-namespace:WorkSpaceManager.Views"
      Title="إدارة العملاء"
      FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="إدارة العملاء" 
                   FontSize="28" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"/>

        <!-- شريط الأدوات -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- البحث -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBox x:Name="TxtSearch"
                         Width="300"
                         Height="35"
                         VerticalContentAlignment="Center"
                         ToolTip="البحث في العملاء..."
                         TextChanged="TxtSearch_TextChanged"/>
                
                <Button Content="🔍 بحث" 
                        Margin="10,0,0,0"
                        Padding="15,5"
                        Background="#2196F3"
                        Foreground="White"
                        Click="BtnSearch_Click"/>
                
                <Button Content="🔄 تحديث" 
                        Margin="10,0,0,0"
                        Padding="15,5"
                        Background="#4CAF50"
                        Foreground="White"
                        Click="BtnRefresh_Click"/>
            </StackPanel>

            <!-- أزرار الإجراءات -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button x:Name="BtnAddCustomer" 
                        Content="➕ إضافة عميل جديد" 
                        Padding="15,10"
                        Background="#4CAF50"
                        Foreground="White"
                        Click="BtnAddCustomer_Click"/>
                
                <Button x:Name="BtnEditCustomer" 
                        Content="✏️ تعديل" 
                        Margin="10,0,0,0"
                        Padding="15,10"
                        Background="#FF9800"
                        Foreground="White"
                        IsEnabled="False"
                        Click="BtnEditCustomer_Click"/>
                
                <Button x:Name="BtnDeleteCustomer" 
                        Content="🗑️ حذف" 
                        Margin="10,0,0,0"
                        Padding="15,10"
                        Background="#F44336"
                        Foreground="White"
                        IsEnabled="False"
                        Click="BtnDeleteCustomer_Click"/>
            </StackPanel>
        </Grid>

        <!-- قائمة العملاء -->
        <Border Grid.Row="2" Padding="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="5">
            <DataGrid x:Name="DgCustomers" 
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      SelectionChanged="DgCustomers_SelectionChanged"
                      MouseDoubleClick="DgCustomers_MouseDoubleClick">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="الرقم" 
                                      Binding="{Binding Id}" 
                                      Width="80"/>
                    
                    <DataGridTextColumn Header="اسم العميل" 
                                      Binding="{Binding Name}" 
                                      Width="200"/>
                    
                    <DataGridTextColumn Header="رقم الهاتف" 
                                      Binding="{Binding Phone}" 
                                      Width="150"/>
                    
                    <DataGridTextColumn Header="البريد الإلكتروني" 
                                      Binding="{Binding Email}" 
                                      Width="200"/>
                    
                    <DataGridTextColumn Header="تاريخ التسجيل"
                                      Binding="{Binding RegistrationDate, StringFormat=\{0:yyyy-MM-dd\}}"
                                      Width="120"/>
                    
                    <DataGridTemplateColumn Header="الحالة" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Border Background="{Binding IsActive, Converter={StaticResource BoolToColorConverter}}" 
                                        CornerRadius="10" 
                                        Padding="8,4">
                                    <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToStatusConverter}}" 
                                             Foreground="White" 
                                             FontWeight="Bold" 
                                             HorizontalAlignment="Center"/>
                                </Border>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                    
                    <DataGridTemplateColumn Header="الإجراءات" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <Button Content="🚀" 
                                            ToolTip="بدء جلسة"
                                            Margin="2"
                                            Padding="8,4"
                                            Background="#4CAF50"
                                            Foreground="White"
                                            Click="BtnStartSession_Click"
                                            Tag="{Binding Id}"/>
                                    
                                    <Button Content="📊" 
                                            ToolTip="عرض التاريخ"
                                            Margin="2"
                                            Padding="8,4"
                                            Background="#2196F3"
                                            Foreground="White"
                                            Click="BtnViewHistory_Click"
                                            Tag="{Binding Id}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>

                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Height" Value="50"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </Border>
    </Grid>

    <Page.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        
        <!-- Custom Converters -->
        <local:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <local:BoolToStatusConverter x:Key="BoolToStatusConverter"/>
    </Page.Resources>
</Page>
