<Window x:Class="WorkSpaceManager.Views.EndShiftWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إنهاء الشيفت" 
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="إنهاء الشيفت" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"
                   HorizontalAlignment="Center"/>

        <!-- النموذج -->
        <StackPanel Grid.Row="1" Margin="0,0,0,20">
            
            <!-- معلومات الشيفت -->
            <Border Background="#F5F5F5" 
                    CornerRadius="5" 
                    Padding="15" 
                    Margin="0,0,0,15">
                <StackPanel>
                    <TextBlock x:Name="TxtShiftInfo" 
                             FontWeight="Bold" 
                             FontSize="16"/>
                    <TextBlock x:Name="TxtShiftDuration" 
                             Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtShiftSales" 
                             Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- إجمالي النقد -->
            <TextBlock Text="إجمالي النقد المحصل: *" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtTotalCash" 
                     Text="0.00"
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"/>

            <!-- إجمالي المصروفات -->
            <TextBlock Text="إجمالي المصروفات:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtTotalExpenses" 
                     Text="0.00"
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"/>

            <!-- ملاحظات -->
            <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtNotes" 
                     Height="60"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"/>

        </StackPanel>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            
            <Button x:Name="BtnEnd" 
                    Content="إنهاء الشيفت" 
                    Background="#F44336" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    Click="BtnEnd_Click"/>
            
            <Button x:Name="BtnCancel" 
                    Content="إلغاء" 
                    Background="#9E9E9E" 
                    Foreground="White" 
                    Padding="20,10"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
