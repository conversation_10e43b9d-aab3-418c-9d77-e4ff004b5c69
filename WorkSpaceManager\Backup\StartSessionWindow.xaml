<Window x:Class="WorkSpaceManager.Views.StartSessionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="بدء جلسة جديدة" 
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="بدء جلسة جديدة" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"
                   HorizontalAlignment="Center"/>

        <!-- النموذج -->
        <StackPanel Grid.Row="1" Margin="0,0,0,20">
            
            <!-- اختيار العميل -->
            <TextBlock Text="العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
            <ComboBox x:Name="CmbCustomers" 
                      DisplayMemberPath="Name" 
                      SelectedValuePath="Id"
                      Margin="0,0,0,15"
                      Height="35"
                      IsEditable="True"
                      IsTextSearchEnabled="True"/>
            
            <Button Content="+ إضافة عميل جديد" 
                    x:Name="BtnAddNewCustomer"
                    Background="Transparent" 
                    Foreground="#2196F3" 
                    BorderThickness="0"
                    HorizontalAlignment="Right"
                    Margin="0,0,0,15"
                    Click="BtnAddNewCustomer_Click"/>

            <!-- سعر الساعة -->
            <TextBlock Text="سعر الساعة (ريال):" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtHourlyRate" 
                     Text="10.00"
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"/>

            <!-- ملاحظات -->
            <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtNotes" 
                     Height="60"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"/>

        </StackPanel>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            
            <Button x:Name="BtnStart" 
                    Content="بدء الجلسة" 
                    Background="#4CAF50" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    Click="BtnStart_Click"/>
            
            <Button x:Name="BtnCancel" 
                    Content="إلغاء" 
                    Background="#F44336" 
                    Foreground="White" 
                    Padding="20,10"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
