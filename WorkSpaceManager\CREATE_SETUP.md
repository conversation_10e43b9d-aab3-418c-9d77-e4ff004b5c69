# دليل إنشاء ملف التثبيت (Setup EXE)

## المتطلبات المسبقة

### 1. تثبيت .NET 6.0 SDK
- تحميل من: https://dotnet.microsoft.com/download/dotnet/6.0
- اختيار "SDK" وليس "Runtime"
- تثبيت النسخة x64

### 2. تثبيت Inno Setup (لإنشاء Setup EXE)
- تحميل من: https://jrsoftware.org/isdl.php
- تثبيت النسخة الأحدث
- اختيار تثبيت كامل مع جميع المكونات

## خطوات البناء والتثبيت

### الخطوة 1: بناء المشروع
```bash
# فتح Command Prompt في مجلد المشروع
cd WorkSpaceManager

# تشغيل ملف البناء
build.bat
```

### الخطوة 2: إنشاء ملف التثبيت
```bash
# بعد نجاح البناء، تشغيل Inno Setup Compiler
# فتح ملف setup.iss في Inno Setup
# أو تشغيل الأمر التالي:
"C:\Program Files (x86)\Inno Setup 6\ISCC.exe" setup.iss
```

### الخطوة 3: اختبار ملف التثبيت
- ملف Setup سيكون في مجلد `installer`
- اسم الملف: `WorkSpaceManager_Setup_v1.0.0.exe`
- اختبار التثبيت على جهاز نظيف

## هيكل الملفات بعد البناء

```
WorkSpaceManager/
├── publish/                    # ملفات التطبيق المبنية
│   ├── WorkSpaceManager.exe   # الملف التنفيذي الرئيسي
│   ├── Data/                  # مجلد البيانات
│   │   ├── Backups/          # النسخ الاحتياطية
│   │   └── Invoices/         # الفواتير المحفوظة
│   └── [ملفات .NET الأخرى]
├── installer/                 # ملف التثبيت
│   └── WorkSpaceManager_Setup_v1.0.0.exe
├── build.bat                 # ملف البناء
├── setup.iss                 # تكوين Inno Setup
└── README.md                 # دليل المستخدم
```

## تخصيص ملف التثبيت

### تغيير الأيقونة
1. إضافة ملف `icon.ico` في مجلد المشروع
2. تحديث `setup.iss`:
```ini
SetupIconFile=icon.ico
```

### تغيير معلومات الشركة
تحديث القيم في `setup.iss`:
```ini
AppPublisher=اسم شركتك
AppPublisherURL=https://موقعك.com
AppSupportURL=https://موقعك.com/support
```

### إضافة ملف ترخيص
1. إنشاء ملف `LICENSE.txt`
2. تحديث `setup.iss`:
```ini
LicenseFile=LICENSE.txt
```

## استكشاف الأخطاء

### خطأ: .NET SDK غير موجود
```
الحل: تثبيت .NET 6.0 SDK من الموقع الرسمي
```

### خطأ: Inno Setup غير موجود
```
الحل: تثبيت Inno Setup وإضافته لمتغير PATH
```

### خطأ: فشل في البناء
```
الحل: 
1. التأكد من وجود جميع ملفات المشروع
2. تشغيل dotnet restore
3. التأكد من صحة ملف .csproj
```

### خطأ: ملف Setup لا يعمل
```
الحل:
1. التأكد من تثبيت .NET Runtime على الجهاز المستهدف
2. تشغيل Setup كمدير (Run as Administrator)
3. التأكد من عدم وجود برامج مكافحة فيروسات تمنع التثبيت
```

## التوزيع

### متطلبات الجهاز المستهدف
- Windows 10 أو أحدث
- .NET 6.0 Runtime (سيتم تثبيته تلقائياً إذا لم يكن موجوداً)
- 4 GB RAM
- 500 MB مساحة فارغة

### طرق التوزيع
1. **USB/CD**: نسخ ملف Setup على وسائط التخزين
2. **التحميل**: رفع على موقع أو خدمة تخزين سحابية
3. **الشبكة المحلية**: مشاركة عبر الشبكة الداخلية

## الصيانة والتحديثات

### إنشاء تحديث جديد
1. تحديث رقم الإصدار في `setup.iss`
2. بناء المشروع مرة أخرى
3. إنشاء ملف Setup جديد
4. اختبار التحديث على إصدار سابق

### النسخ الاحتياطية
- نسخ مجلد `Data` قبل التحديث
- الاحتفاظ بنسخة من ملف Setup القديم
- توثيق التغييرات في كل إصدار

## الدعم الفني

### ملفات السجلات
- مجلد التثبيت: `C:\Program Files\Work Space Manager\`
- مجلد البيانات: `C:\Program Files\Work Space Manager\Data\`
- سجلات Windows: Event Viewer > Application

### معلومات مفيدة للدعم
- رقم إصدار البرنامج
- نسخة Windows
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

---
**ملاحظة**: هذا الدليل مخصص للمطورين والمسؤولين عن النظام. للمستخدمين العاديين، يرجى الرجوع إلى ملف README.md.
