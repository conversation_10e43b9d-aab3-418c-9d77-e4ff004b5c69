using System;
using Microsoft.Data.Sqlite;
using System.IO;
using System.Collections.Generic;
using WorkSpaceManager.Models;

namespace WorkSpaceManager.Data
{
    public static class DatabaseService
    {
        private static string _connectionString;
        
        public static string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "workspace.db");
                    _connectionString = $"Data Source={dbPath};Version=3;";
                }
                return _connectionString;
            }
        }

        public static void InitializeDatabase()
        {
            using var connection = new SqliteConnection(ConnectionString);
            connection.Open();

            // إنشاء جدول العملاء
            var createCustomersTable = @"
                CREATE TABLE IF NOT EXISTS Customers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Phone TEXT,
                    Email TEXT,
                    RegistrationDate DATETIME NOT NULL,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    Notes TEXT
                )";

            // إنشاء جدول الجلسات
            var createSessionsTable = @"
                CREATE TABLE IF NOT EXISTS Sessions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    CustomerId INTEGER NOT NULL,
                    StartTime DATETIME NOT NULL,
                    EndTime DATETIME,
                    HourlyRate DECIMAL(10,2) NOT NULL,
                    TotalHours DECIMAL(10,2) DEFAULT 0,
                    TotalAmount DECIMAL(10,2) DEFAULT 0,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    Notes TEXT,
                    ShiftId INTEGER,
                    FOREIGN KEY (CustomerId) REFERENCES Customers(Id),
                    FOREIGN KEY (ShiftId) REFERENCES Shifts(Id)
                )";

            // إنشاء جدول المنتجات
            var createProductsTable = @"
                CREATE TABLE IF NOT EXISTS Products (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    Category TEXT,
                    Price DECIMAL(10,2) NOT NULL,
                    Cost DECIMAL(10,2) DEFAULT 0,
                    Quantity INTEGER DEFAULT 0,
                    Barcode TEXT,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    Description TEXT
                )";

            // إنشاء جدول المشتريات
            var createPurchasesTable = @"
                CREATE TABLE IF NOT EXISTS Purchases (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    SessionId INTEGER NOT NULL,
                    ProductId INTEGER NOT NULL,
                    Quantity INTEGER NOT NULL,
                    UnitPrice DECIMAL(10,2) NOT NULL,
                    TotalPrice DECIMAL(10,2) NOT NULL,
                    PurchaseTime DATETIME NOT NULL,
                    Notes TEXT,
                    FOREIGN KEY (SessionId) REFERENCES Sessions(Id),
                    FOREIGN KEY (ProductId) REFERENCES Products(Id)
                )";

            // إنشاء جدول الشيفتات
            var createShiftsTable = @"
                CREATE TABLE IF NOT EXISTS Shifts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    StartTime DATETIME NOT NULL,
                    EndTime DATETIME,
                    EmployeeName TEXT,
                    TotalSales DECIMAL(10,2) DEFAULT 0,
                    TotalCash DECIMAL(10,2) DEFAULT 0,
                    TotalExpenses DECIMAL(10,2) DEFAULT 0,
                    NetProfit DECIMAL(10,2) DEFAULT 0,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    Notes TEXT
                )";

            using var command = new SqliteCommand(connection);
            
            command.CommandText = createCustomersTable;
            command.ExecuteNonQuery();
            
            command.CommandText = createSessionsTable;
            command.ExecuteNonQuery();
            
            command.CommandText = createProductsTable;
            command.ExecuteNonQuery();
            
            command.CommandText = createPurchasesTable;
            command.ExecuteNonQuery();
            
            command.CommandText = createShiftsTable;
            command.ExecuteNonQuery();

            // إدراج بيانات افتراضية
            InsertDefaultData(connection);
        }

        private static void InsertDefaultData(SqliteConnection connection)
        {
            // إدراج منتجات افتراضية
            var insertProducts = @"
                INSERT OR IGNORE INTO Products (Id, Name, Category, Price, Cost, IsActive) VALUES
                (1, 'قهوة', 'مشروبات', 5.00, 2.00, 1),
                (2, 'شاي', 'مشروبات', 3.00, 1.00, 1),
                (3, 'عصير', 'مشروبات', 8.00, 4.00, 1),
                (4, 'ساندويتش', 'طعام', 15.00, 8.00, 1),
                (5, 'بسكويت', 'وجبات خفيفة', 4.00, 2.00, 1)";

            using var command = new SqliteCommand(insertProducts, connection);
            command.ExecuteNonQuery();
        }
    }
}
