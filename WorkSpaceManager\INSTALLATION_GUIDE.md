# دليل التثبيت والتشغيل - Work Space Manager

## نظرة عامة
هذا الدليل يوضح كيفية تثبيت وتشغيل برنامج Work Space Manager على نظام Windows.

## المتطلبات المسبقة

### متطلبات النظام
- **نظام التشغيل**: Windows 10 أو أحدث (64-bit)
- **الذاكرة**: 4 GB RAM (الحد الأدنى)
- **التخزين**: 500 MB مساحة فارغة
- **دقة الشاشة**: 1024x768 (الحد الأدنى)
- **الشبكة**: غير مطلوبة (البرنامج يعمل محلياً)

### البرامج المطلوبة
- **.NET 6.0 Runtime** (سيتم تثبيته تلقائياً إذا لم يكن موجوداً)

## طرق التثبيت

### الطريقة الأولى: استخدام ملف Setup EXE (الموصى بها)

1. **تحميل ملف التثبيت**
   - احصل على ملف `WorkSpaceManager_Setup_v1.0.0.exe`

2. **تشغيل المثبت**
   - انقر بزر الماوس الأيمن على ملف Setup
   - اختر "Run as administrator" (تشغيل كمدير)
   - اتبع خطوات المعالج

3. **اختيار مجلد التثبيت**
   - المجلد الافتراضي: `C:\Program Files\Work Space Manager\`
   - يمكنك تغيير المجلد حسب الحاجة

4. **إنهاء التثبيت**
   - انتظر حتى انتهاء عملية التثبيت
   - اختر تشغيل البرنامج فوراً (اختياري)

### الطريقة الثانية: التشغيل المباشر (للمطورين)

1. **تثبيت .NET 6.0 SDK**
   ```
   تحميل من: https://dotnet.microsoft.com/download/dotnet/6.0
   ```

2. **بناء المشروع**
   ```bash
   cd WorkSpaceManager
   dotnet restore
   dotnet build --configuration Release
   ```

3. **تشغيل البرنامج**
   ```bash
   dotnet run
   ```

## التشغيل لأول مرة

### الخطوات الأولى
1. **تشغيل البرنامج**
   - من سطح المكتب: انقر مزدوجاً على أيقونة Work Space Manager
   - من قائمة Start: ابحث عن "Work Space Manager"

2. **التهيئة التلقائية**
   - سيتم إنشاء قاعدة البيانات تلقائياً
   - سيتم إنشاء مجلدات البيانات والنسخ الاحتياطية
   - سيتم إدراج منتجات افتراضية

3. **التحقق من التثبيت**
   - تأكد من ظهور الواجهة الرئيسية
   - تحقق من وجود البيانات الافتراضية

### الإعدادات الأولية
1. **بدء شيفت جديد**
   - انقر على "بدء شيفت جديد" من الصفحة الرئيسية
   - أدخل اسم الشيفت واسم الموظف

2. **إضافة عميل تجريبي**
   - اذهب إلى "إدارة العملاء"
   - انقر "إضافة عميل جديد"
   - أدخل بيانات تجريبية

3. **اختبار الجلسة**
   - ابدأ جلسة جديدة للعميل التجريبي
   - أضف بعض المشتريات
   - أنهِ الجلسة وأنشئ فاتورة

## هيكل الملفات بعد التثبيت

```
C:\Program Files\Work Space Manager\
├── WorkSpaceManager.exe          # الملف التنفيذي الرئيسي
├── Data\                         # مجلد البيانات
│   ├── workspace.db             # قاعدة البيانات الرئيسية
│   ├── Backups\                 # النسخ الاحتياطية التلقائية
│   └── Invoices\                # الفواتير المحفوظة
├── Resources\                    # الموارد والأنماط
├── [ملفات .NET الأخرى]
└── README.md                     # دليل المستخدم
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ: "لا يمكن تشغيل البرنامج"
**السبب**: .NET Runtime غير مثبت
**الحل**: 
- تحميل وتثبيت .NET 6.0 Runtime من الموقع الرسمي
- إعادة تشغيل الكمبيوتر
- تشغيل البرنامج مرة أخرى

#### 2. خطأ: "خطأ في تهيئة قاعدة البيانات"
**السبب**: مشكلة في الصلاحيات أو مساحة التخزين
**الحل**:
- تشغيل البرنامج كمدير (Run as Administrator)
- التأكد من وجود مساحة كافية على القرص الصلب
- التأكد من عدم حجب برامج مكافحة الفيروسات للبرنامج

#### 3. خطأ: "فشل في الطباعة"
**السبب**: مشكلة في إعدادات الطابعة
**الحل**:
- التأكد من تثبيت تعريف الطابعة
- التأكد من اتصال الطابعة
- تجربة طباعة من برنامج آخر للتأكد

#### 4. البرنامج بطيء
**الأسباب المحتملة**:
- تراكم النسخ الاحتياطية القديمة
- قاعدة بيانات كبيرة
- مواصفات الجهاز ضعيفة

**الحلول**:
- تنظيف النسخ الاحتياطية القديمة
- إعادة تشغيل البرنامج
- إعادة تشغيل الكمبيوتر

### ملفات السجلات
- **مجلد البيانات**: `C:\Program Files\Work Space Manager\Data\`
- **النسخ الاحتياطية**: `Data\Backups\`
- **سجلات Windows**: Event Viewer > Application

## النسخ الاحتياطي والاستعادة

### النسخ الاحتياطي التلقائي
- يتم إنشاء نسخة احتياطية كل 30 دقيقة تلقائياً
- يتم الاحتفاظ بآخر 30 نسخة احتياطية
- مكان الحفظ: `Data\Backups\`

### النسخ الاحتياطي اليدوي
1. إغلاق البرنامج
2. نسخ مجلد `Data` بالكامل
3. حفظه في مكان آمن (USB، سحابة، إلخ)

### الاستعادة من النسخة الاحتياطية
1. إغلاق البرنامج
2. استبدال ملف `workspace.db` بالنسخة الاحتياطية
3. تشغيل البرنامج مرة أخرى

## التحديثات

### تحديث البرنامج
1. تحميل الإصدار الجديد
2. تشغيل ملف Setup الجديد
3. سيتم الاحتفاظ بالبيانات تلقائياً

### نقل البيانات لجهاز جديد
1. نسخ مجلد `Data` من الجهاز القديم
2. تثبيت البرنامج على الجهاز الجديد
3. استبدال مجلد `Data` الجديد بالقديم

## الدعم الفني

### معلومات مطلوبة عند طلب الدعم
- رقم إصدار البرنامج
- نسخة Windows
- وصف المشكلة بالتفصيل
- خطوات إعادة إنتاج المشكلة
- لقطة شاشة للخطأ (إن وجد)

### طرق التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: www.workspacemanager.com
- **الهاتف**: +966-XX-XXX-XXXX

## نصائح للاستخدام الأمثل

### الأداء
- إعادة تشغيل البرنامج يومياً
- تنظيف النسخ الاحتياطية القديمة شهرياً
- عدم تشغيل برامج ثقيلة أخرى مع البرنامج

### الأمان
- نسخ احتياطي أسبوعي خارجي
- عدم حذف مجلد البيانات
- تشغيل البرنامج بصلاحيات محدودة عند الإمكان

### الصيانة
- تحديث Windows بانتظام
- تحديث تعريفات الطابعة
- فحص دوري لمساحة القرص الصلب

---
**Work Space Manager v1.0.0**  
© 2024 Work Space Solutions. جميع الحقوق محفوظة.
