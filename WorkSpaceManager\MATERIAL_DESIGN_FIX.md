# إصلاح مشاكل Material Design

## المشكلة
```
error MC3074: The tag 'BundledTheme' does not exist in XML namespace 'http://materialdesigninxaml.net/winfx/xaml/themes'
error MC3074: The tag 'Card' does not exist in XML namespace
error MC3072: The property 'HintAssist.Hint' does not exist in XML namespace
```

## الحلول المتاحة

### الحل الأول: إصلاح تلقائي (الموصى به)
```bash
build-auto-fix.bat
```

هذا الملف سيجرب:
1. البناء مع Material Design
2. إذا فشل، سيستخدم إصدار مبسط بدون Material Design

### الحل الثاني: بناء مع Material Design
```bash
dotnet restore WorkSpaceManager.csproj
dotnet build WorkSpaceManager.csproj
```

### الحل الثالث: بناء بدون Material Design
```bash
# استخدام ملف المشروع المبسط
copy WorkSpaceManager-NoMaterial.csproj WorkSpaceManager.csproj
dotnet restore
dotnet build
```

## إصلاح ملفات XAML يدوياً

### إصلاح App.xaml
استبدال:
```xml
xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
<materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Lime" />
```

بـ:
```xml
<!-- Basic WPF styles -->
```

### إصلاح ملفات Views
استبدال:
```xml
<materialDesign:Card>
```
بـ:
```xml
<Border Style="{StaticResource CardStyle}">
```

استبدال:
```xml
materialDesign:HintAssist.Hint="النص"
```
بـ:
```xml
ToolTip="النص"
```

## الملفات المتأثرة

يجب إصلاح هذه الملفات:
- `App.xaml`
- `Views/MainWindow.xaml`
- `Views/DashboardPage.xaml`
- `Views/CustomersPage.xaml`
- `Views/ProductsPage.xaml`
- `Views/CreateInvoiceWindow.xaml`
- `Views/CustomerHistoryWindow.xaml`

## البدائل المتاحة

### 1. استخدام WPF العادي
- أسرع في البناء
- أقل تعقيداً
- يعمل بدون مشاكل

### 2. استخدام Material Design
- تصميم أجمل
- يحتاج حزم إضافية
- قد يسبب مشاكل في البناء

## الأوامر السريعة

### بناء سريع بدون Material Design:
```bash
# نسخ الملفات المبسطة
copy App-Simple.xaml App.xaml

# بناء
dotnet clean
dotnet restore
dotnet build
```

### إضافة Material Design لاحقاً:
```bash
dotnet add package MaterialDesignThemes --version 4.6.1
dotnet add package MaterialDesignColors --version 2.0.9
```

## نصائح

1. **ابدأ بالإصدار المبسط** للتأكد من عمل البرنامج
2. **أضف Material Design لاحقاً** بعد التأكد من الوظائف الأساسية
3. **استخدم build-auto-fix.bat** للإصلاح التلقائي

## التحقق من النجاح

يجب أن ترى:
```
Build succeeded.
    0 Warning(s)
    0 Error(s)
```

ثم يمكنك تشغيل البرنامج:
```bash
dotnet run
```
