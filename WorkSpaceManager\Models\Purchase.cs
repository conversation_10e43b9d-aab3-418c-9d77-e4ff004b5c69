using System;
using System.ComponentModel;

namespace WorkSpaceManager.Models
{
    public class Purchase : INotifyPropertyChanged
    {
        private int _id;
        private int _sessionId;
        private int _productId;
        private string _productName;
        private int _quantity;
        private decimal _unitPrice;
        private decimal _totalPrice;
        private DateTime _purchaseTime;
        private string _notes;

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(nameof(Id)); }
        }

        public int SessionId
        {
            get => _sessionId;
            set { _sessionId = value; OnPropertyChanged(nameof(SessionId)); }
        }

        public int ProductId
        {
            get => _productId;
            set { _productId = value; OnPropertyChanged(nameof(ProductId)); }
        }

        public string ProductName
        {
            get => _productName;
            set { _productName = value; OnPropertyChanged(nameof(ProductName)); }
        }

        public int Quantity
        {
            get => _quantity;
            set 
            { 
                _quantity = value; 
                OnPropertyChanged(nameof(Quantity));
                CalculateTotalPrice();
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set 
            { 
                _unitPrice = value; 
                OnPropertyChanged(nameof(UnitPrice));
                CalculateTotalPrice();
            }
        }

        public decimal TotalPrice
        {
            get => _totalPrice;
            set { _totalPrice = value; OnPropertyChanged(nameof(TotalPrice)); }
        }

        public DateTime PurchaseTime
        {
            get => _purchaseTime;
            set { _purchaseTime = value; OnPropertyChanged(nameof(PurchaseTime)); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(nameof(Notes)); }
        }

        private void CalculateTotalPrice()
        {
            TotalPrice = Quantity * UnitPrice;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
