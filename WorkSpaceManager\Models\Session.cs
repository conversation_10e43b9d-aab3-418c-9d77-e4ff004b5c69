using System;
using System.ComponentModel;

namespace WorkSpaceManager.Models
{
    public class Session : INotifyPropertyChanged
    {
        private int _id;
        private int _customerId;
        private string _customerName;
        private DateTime _startTime;
        private DateTime? _endTime;
        private decimal _hourlyRate;
        private decimal _totalHours;
        private decimal _totalAmount;
        private bool _isActive;
        private string _notes;
        private int _shiftId;

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(nameof(Id)); }
        }

        public int CustomerId
        {
            get => _customerId;
            set { _customerId = value; OnPropertyChanged(nameof(CustomerId)); }
        }

        public string CustomerName
        {
            get => _customerName;
            set { _customerName = value; OnPropertyChanged(nameof(CustomerName)); }
        }

        public DateTime StartTime
        {
            get => _startTime;
            set { _startTime = value; OnPropertyChanged(nameof(StartTime)); }
        }

        public DateTime? EndTime
        {
            get => _endTime;
            set 
            { 
                _endTime = value; 
                OnPropertyChanged(nameof(EndTime));
                CalculateTotalHours();
            }
        }

        public decimal HourlyRate
        {
            get => _hourlyRate;
            set 
            { 
                _hourlyRate = value; 
                OnPropertyChanged(nameof(HourlyRate));
                CalculateTotalAmount();
            }
        }

        public decimal TotalHours
        {
            get => _totalHours;
            set 
            { 
                _totalHours = value; 
                OnPropertyChanged(nameof(TotalHours));
                CalculateTotalAmount();
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set { _totalAmount = value; OnPropertyChanged(nameof(TotalAmount)); }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(nameof(IsActive)); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(nameof(Notes)); }
        }

        public int ShiftId
        {
            get => _shiftId;
            set { _shiftId = value; OnPropertyChanged(nameof(ShiftId)); }
        }

        private void CalculateTotalHours()
        {
            if (EndTime.HasValue)
            {
                var duration = EndTime.Value - StartTime;
                TotalHours = (decimal)duration.TotalHours;
            }
        }

        private void CalculateTotalAmount()
        {
            TotalAmount = TotalHours * HourlyRate;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
