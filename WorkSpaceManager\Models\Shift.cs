using System;
using System.ComponentModel;

namespace WorkSpaceManager.Models
{
    public class Shift : INotifyPropertyChanged
    {
        private int _id;
        private string _name;
        private DateTime _startTime;
        private DateTime? _endTime;
        private string _employeeName;
        private decimal _totalSales;
        private decimal _totalCash;
        private decimal _totalExpenses;
        private decimal _netProfit;
        private bool _isActive;
        private string _notes;

        public int Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(nameof(Id)); }
        }

        public string Name
        {
            get => _name;
            set { _name = value; OnPropertyChanged(nameof(Name)); }
        }

        public DateTime StartTime
        {
            get => _startTime;
            set { _startTime = value; OnPropertyChanged(nameof(StartTime)); }
        }

        public DateTime? EndTime
        {
            get => _endTime;
            set { _endTime = value; OnPropertyChanged(nameof(EndTime)); }
        }

        public string EmployeeName
        {
            get => _employeeName;
            set { _employeeName = value; OnPropertyChanged(nameof(EmployeeName)); }
        }

        public decimal TotalSales
        {
            get => _totalSales;
            set 
            { 
                _totalSales = value; 
                OnPropertyChanged(nameof(TotalSales));
                CalculateNetProfit();
            }
        }

        public decimal TotalCash
        {
            get => _totalCash;
            set { _totalCash = value; OnPropertyChanged(nameof(TotalCash)); }
        }

        public decimal TotalExpenses
        {
            get => _totalExpenses;
            set 
            { 
                _totalExpenses = value; 
                OnPropertyChanged(nameof(TotalExpenses));
                CalculateNetProfit();
            }
        }

        public decimal NetProfit
        {
            get => _netProfit;
            set { _netProfit = value; OnPropertyChanged(nameof(NetProfit)); }
        }

        public bool IsActive
        {
            get => _isActive;
            set { _isActive = value; OnPropertyChanged(nameof(IsActive)); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(nameof(Notes)); }
        }

        private void CalculateNetProfit()
        {
            NetProfit = TotalSales - TotalExpenses;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
