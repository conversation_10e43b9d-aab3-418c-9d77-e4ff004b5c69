# إصلاح سريع لمشكلة البناء

## المشكلة
```
MSBUILD : error MSB1011: Specify which project or solution file to use because this folder contains more than one project or solution file.
```

## الحل السريع

### الخطوة 1: تنظيف المجلد
```bash
# احذف الملفات الإضافية
del WorkSpaceManager-Simple.csproj
del WorkSpaceManager-Minimal.csproj
```

### الخطوة 2: استخدام البناء النظيف
```bash
build-clean.bat
```

### الخطوة 3: أو استخدام الأوامر المباشرة
```bash
dotnet clean WorkSpaceManager.csproj
dotnet restore WorkSpaceManager.csproj
dotnet build WorkSpaceManager.csproj --configuration Release
```

### الخطوة 4: تشغيل البرنامج
```bash
dotnet run --project WorkSpaceManager.csproj
```

## إذا استمرت المشكلة

### الحل البديل 1: إنشاء مجلد جديد
```bash
mkdir WorkSpaceManager_Clean
copy *.cs WorkSpaceManager_Clean\
copy *.xaml WorkSpaceManager_Clean\
cd WorkSpaceManager_Clean
```

### الحل البديل 2: استخدام Visual Studio
1. فتح Visual Studio
2. File → Open → Project/Solution
3. اختيار `WorkSpaceManager.csproj` فقط
4. Build → Rebuild Solution

### الحل البديل 3: إنشاء مشروع جديد
```bash
dotnet new wpf -n WorkSpaceManagerNew
# ثم نسخ الملفات
```

## التحقق من نجاح الإصلاح

يجب أن ترى:
```
Build succeeded.
    0 Warning(s)
    0 Error(s)
```

## ملفات البناء المتاحة

- `build-clean.bat` - بناء نظيف مع إصلاح تلقائي
- `build-simple.bat` - بناء مبسط (محدث)
- `run.bat` - تشغيل البرنامج (محدث)

## نصائح إضافية

1. **تأكد من وجود ملف واحد فقط `.csproj`**
2. **استخدم اسم المشروع الصريح في الأوامر**
3. **نظف الكاش إذا استمرت المشاكل:**
   ```bash
   dotnet nuget locals all --clear
   ```

## الأوامر الأساسية

```bash
# تنظيف
dotnet clean WorkSpaceManager.csproj

# استعادة الحزم
dotnet restore WorkSpaceManager.csproj

# البناء
dotnet build WorkSpaceManager.csproj --configuration Release

# التشغيل
dotnet run --project WorkSpaceManager.csproj

# النشر
dotnet publish WorkSpaceManager.csproj -c Release -o publish
```
