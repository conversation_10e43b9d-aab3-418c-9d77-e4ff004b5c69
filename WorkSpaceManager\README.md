# Work Space Manager - نظام إدارة المساحات المشتركة

## نظرة عامة
Work Space Manager هو برنامج سطح مكتب احترافي مصمم خصيصاً لإدارة المساحات المشتركة (Co-working Spaces) في البيئة العربية. يوفر البرنامج حلولاً شاملة لإدارة العملاء، تتبع الجلسات، إدارة المبيعات، والتقارير المالية.

## المميزات الرئيسية

### 🏠 لوحة التحكم الرئيسية
- عرض الإحصائيات المباشرة (الجلسات النشطة، المبيعات اليومية، إجمالي العملاء)
- الإجراءات السريعة (بدء جلسة، إضافة عميل، بدء شيفت)
- عرض الأنشطة الأخيرة

### 👥 إدارة العملاء
- إضافة وتعديل وحذف العملاء
- تسجيل معلومات الاتصال والملاحظات
- البحث السريع في قاعدة بيانات العملاء

### ⏰ إدارة الجلسات
- بدء وإنهاء الجلسات بدقة
- حساب المدة والتكلفة تلقائياً
- تتبع الجلسات النشطة في الوقت الفعلي

### 🛍️ إدارة المنتجات والمشروبات
- إدارة قائمة المنتجات والأسعار
- إضافة المشتريات للجلسات
- حساب الأرباح الصافية

### 🧾 نظام الفواتير
- إنشاء فواتير فورية شاملة
- طباعة على الطابعات الحرارية (POS)
- حفظ الفواتير محلياً

### 🔄 إدارة الشيفتات
- بدء وإنهاء الشيفتات
- حساب مبيعات كل شيفت
- تتبع النقد والمصروفات

### 📊 التقارير والإحصائيات
- تقارير يومية وأسبوعية وشهرية
- حساب الأرباح الصافية
- إحصائيات مفصلة للأداء

### 💾 النسخ الاحتياطي التلقائي
- نسخ احتياطي تلقائي كل 30 دقيقة
- إمكانية الاستعادة من النسخ السابقة
- تخزين محلي آمن

### 🌙 الوضع الليلي
- دعم الوضع الليلي لراحة العينين
- تبديل سهل بين الأوضاع

## المتطلبات التقنية

### متطلبات النظام
- Windows 10 أو أحدث
- .NET 6.0 Runtime
- 4 GB RAM (الحد الأدنى)
- 500 MB مساحة تخزين
- دقة شاشة 1024x768 (الحد الأدنى)

### قاعدة البيانات
- SQLite (مدمجة مع البرنامج)
- تخزين محلي بدون الحاجة لسيرفر
- نسخ احتياطي تلقائي

## التثبيت والتشغيل

### طريقة التثبيت
1. تحميل ملف Setup.exe
2. تشغيل المثبت كمدير (Run as Administrator)
3. اتباع خطوات التثبيت
4. تشغيل البرنامج من سطح المكتب

### التشغيل لأول مرة
1. سيتم إنشاء قاعدة البيانات تلقائياً
2. إدراج بيانات افتراضية (منتجات أساسية)
3. البرنامج جاهز للاستخدام فوراً

## دليل الاستخدام السريع

### بدء العمل
1. **بدء شيفت جديد**: من الصفحة الرئيسية أو قائمة الشيفتات
2. **إضافة عميل**: من قائمة العملاء أو الإجراءات السريعة
3. **بدء جلسة**: اختيار العميل وتحديد سعر الساعة

### إدارة الجلسات
1. **بدء جلسة**: اختيار العميل + سعر الساعة
2. **إضافة مشتريات**: من قائمة المنتجات أثناء الجلسة
3. **إنهاء جلسة**: حساب تلقائي للمدة والتكلفة

### إنشاء الفواتير
1. اختيار الجلسة المطلوبة
2. مراجعة التفاصيل (الوقت + المشتريات)
3. طباعة أو حفظ الفاتورة

### إنهاء الشيفت
1. تسجيل النقد المحصل
2. إدخال المصروفات
3. مراجعة الأرباح الصافية

## الدعم الفني

### ملفات السجلات
- مجلد البيانات: `[مجلد البرنامج]/Data/`
- قاعدة البيانات: `workspace.db`
- النسخ الاحتياطية: `Data/Backups/`
- الفواتير المحفوظة: `Data/Invoices/`

### استكشاف الأخطاء
1. **خطأ في قاعدة البيانات**: استعادة من النسخة الاحتياطية
2. **مشكلة في الطباعة**: التأكد من تعريف الطابعة
3. **بطء في الأداء**: تنظيف النسخ الاحتياطية القديمة

### النسخ الاحتياطي اليدوي
- نسخ مجلد `Data` بالكامل
- حفظ في مكان آمن خارج مجلد البرنامج

## الأمان والخصوصية
- جميع البيانات محفوظة محلياً
- لا يتم إرسال أي بيانات عبر الإنترنت
- تشفير قاعدة البيانات (اختياري)
- نسخ احتياطي تلقائي لحماية البيانات

## التحديثات المستقبلية
- دعم قواعد بيانات خارجية
- تطبيق موبايل مصاحب
- تقارير متقدمة مع الرسوم البيانية
- دعم عدة فروع
- نظام إدارة المخزون

## معلومات الترخيص
- البرنامج مجاني للاستخدام التجاري
- يمنع إعادة التوزيع بدون إذن
- الدعم الفني متاح عبر البريد الإلكتروني

## معلومات الاتصال
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: www.workspacemanager.com
- الهاتف: +966-XX-XXX-XXXX

---
**Work Space Manager v1.0.0**  
© 2024 Work Space Solutions. جميع الحقوق محفوظة.
