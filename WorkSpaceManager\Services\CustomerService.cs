using System;
using System.Collections.Generic;
using Microsoft.Data.Sqlite;
using WorkSpaceManager.Data;
using WorkSpaceManager.Models;

namespace WorkSpaceManager.Services
{
    public static class CustomerService
    {
        public static List<Customer> GetAllCustomers()
        {
            var customers = new List<Customer>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT * FROM Customers WHERE IsActive = 1 ORDER BY Name";
            using var command = new SqliteCommand(query, connection);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                customers.Add(new Customer
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Phone = reader.IsDBNull("Phone") ? "" : reader.GetString("Phone"),
                    Email = reader.IsDBNull("Email") ? "" : reader.GetString("Email"),
                    RegistrationDate = reader.GetDateTime("RegistrationDate"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                });
            }
            
            return customers;
        }

        public static Customer GetCustomerById(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT * FROM Customers WHERE Id = @id";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            using var reader = command.ExecuteReader();
            
            if (reader.Read())
            {
                return new Customer
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Phone = reader.IsDBNull("Phone") ? "" : reader.GetString("Phone"),
                    Email = reader.IsDBNull("Email") ? "" : reader.GetString("Email"),
                    RegistrationDate = reader.GetDateTime("RegistrationDate"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                };
            }
            
            return null;
        }

        public static int AddCustomer(Customer customer)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"INSERT INTO Customers (Name, Phone, Email, RegistrationDate, IsActive, Notes) 
                         VALUES (@name, @phone, @email, @registrationDate, @isActive, @notes);
                         SELECT last_insert_rowid();";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@name", customer.Name);
            command.Parameters.AddWithValue("@phone", customer.Phone ?? "");
            command.Parameters.AddWithValue("@email", customer.Email ?? "");
            command.Parameters.AddWithValue("@registrationDate", customer.RegistrationDate);
            command.Parameters.AddWithValue("@isActive", customer.IsActive);
            command.Parameters.AddWithValue("@notes", customer.Notes ?? "");
            
            return Convert.ToInt32(command.ExecuteScalar());
        }

        public static bool UpdateCustomer(Customer customer)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"UPDATE Customers SET 
                         Name = @name, Phone = @phone, Email = @email, 
                         IsActive = @isActive, Notes = @notes 
                         WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", customer.Id);
            command.Parameters.AddWithValue("@name", customer.Name);
            command.Parameters.AddWithValue("@phone", customer.Phone ?? "");
            command.Parameters.AddWithValue("@email", customer.Email ?? "");
            command.Parameters.AddWithValue("@isActive", customer.IsActive);
            command.Parameters.AddWithValue("@notes", customer.Notes ?? "");
            
            return command.ExecuteNonQuery() > 0;
        }

        public static bool DeleteCustomer(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "UPDATE Customers SET IsActive = 0 WHERE Id = @id";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            return command.ExecuteNonQuery() > 0;
        }

        public static List<Customer> SearchCustomers(string searchTerm)
        {
            var customers = new List<Customer>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"SELECT * FROM Customers 
                         WHERE IsActive = 1 AND (Name LIKE @search OR Phone LIKE @search) 
                         ORDER BY Name";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@search", $"%{searchTerm}%");
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                customers.Add(new Customer
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Phone = reader.IsDBNull("Phone") ? "" : reader.GetString("Phone"),
                    Email = reader.IsDBNull("Email") ? "" : reader.GetString("Email"),
                    RegistrationDate = reader.GetDateTime("RegistrationDate"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                });
            }
            
            return customers;
        }
    }
}
