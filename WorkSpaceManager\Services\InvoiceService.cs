using System;
using System.IO;
using System.Linq;
using System.Text;
using WorkSpaceManager.Models;

namespace WorkSpaceManager.Services
{
    public static class InvoiceService
    {
        public static void SaveInvoice(Session session)
        {
            try
            {
                var invoicesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Invoices");
                if (!Directory.Exists(invoicesPath))
                {
                    Directory.CreateDirectory(invoicesPath);
                }

                var fileName = $"Invoice_{session.Id}_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                var filePath = Path.Combine(invoicesPath, fileName);

                var invoiceContent = GenerateInvoiceContent(session);
                File.WriteAllText(filePath, invoiceContent, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الفاتورة: {ex.Message}");
            }
        }

        private static string GenerateInvoiceContent(Session session)
        {
            var sb = new StringBuilder();
            
            // رأس الفاتورة
            sb.AppendLine("===============================================");
            sb.AppendLine("           Work Space Manager");
            sb.AppendLine("          فاتورة جلسة عمل");
            sb.AppendLine("===============================================");
            sb.AppendLine();

            // معلومات الفاتورة
            sb.AppendLine($"رقم الفاتورة: INV-{session.Id:000000}");
            sb.AppendLine($"تاريخ الفاتورة: {DateTime.Now:yyyy-MM-dd HH:mm}");
            sb.AppendLine();

            // معلومات العميل
            var customer = CustomerService.GetCustomerById(session.CustomerId);
            if (customer != null)
            {
                sb.AppendLine("معلومات العميل:");
                sb.AppendLine($"الاسم: {customer.Name}");
                if (!string.IsNullOrEmpty(customer.Phone))
                    sb.AppendLine($"الهاتف: {customer.Phone}");
                if (!string.IsNullOrEmpty(customer.Email))
                    sb.AppendLine($"البريد الإلكتروني: {customer.Email}");
                sb.AppendLine();
            }

            // معلومات الجلسة
            sb.AppendLine("تفاصيل الجلسة:");
            sb.AppendLine($"وقت البداية: {session.StartTime:yyyy-MM-dd HH:mm}");
            if (session.EndTime.HasValue)
                sb.AppendLine($"وقت النهاية: {session.EndTime:yyyy-MM-dd HH:mm}");
            sb.AppendLine($"المدة: {session.TotalHours:F2} ساعة");
            sb.AppendLine($"سعر الساعة: {session.HourlyRate:F2} ريال");
            sb.AppendLine($"إجمالي الوقت: {session.TotalAmount:F2} ريال");
            sb.AppendLine();

            // المشتريات
            var purchases = PurchaseService.GetPurchasesBySession(session.Id);
            if (purchases.Any())
            {
                sb.AppendLine("المشتريات:");
                sb.AppendLine("-----------------------------------------------");
                sb.AppendLine("المنتج\t\tالكمية\tالسعر\tالإجمالي");
                sb.AppendLine("-----------------------------------------------");

                decimal purchasesTotal = 0;
                foreach (var purchase in purchases)
                {
                    sb.AppendLine($"{purchase.ProductName}\t\t{purchase.Quantity}\t{purchase.UnitPrice:F2}\t{purchase.TotalPrice:F2}");
                    purchasesTotal += purchase.TotalPrice;
                }

                sb.AppendLine("-----------------------------------------------");
                sb.AppendLine($"إجمالي المشتريات: {purchasesTotal:F2} ريال");
                sb.AppendLine();
            }

            // الإجمالي النهائي
            var grandTotal = session.TotalAmount + purchases.Sum(p => p.TotalPrice);
            sb.AppendLine("===============================================");
            sb.AppendLine($"الإجمالي النهائي: {grandTotal:F2} ريال");
            sb.AppendLine("===============================================");
            sb.AppendLine();

            // رسالة شكر
            sb.AppendLine("شكراً لزيارتكم");
            sb.AppendLine("نتطلع لزيارتكم مرة أخرى");
            sb.AppendLine();
            sb.AppendLine($"تم إنشاء الفاتورة في: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

            return sb.ToString();
        }

        public static string[] GetSavedInvoices()
        {
            try
            {
                var invoicesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Invoices");
                if (!Directory.Exists(invoicesPath))
                {
                    return new string[0];
                }

                var invoiceFiles = Directory.GetFiles(invoicesPath, "Invoice_*.txt");
                Array.Sort(invoiceFiles);
                Array.Reverse(invoiceFiles); // الأحدث أولاً
                
                return invoiceFiles;
            }
            catch
            {
                return new string[0];
            }
        }

        public static string ReadInvoice(string filePath)
        {
            try
            {
                return File.ReadAllText(filePath, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في قراءة الفاتورة: {ex.Message}");
            }
        }

        public static void DeleteInvoice(string filePath)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الفاتورة: {ex.Message}");
            }
        }
    }
}
