using System;
using System.Collections.Generic;
using Microsoft.Data.Sqlite;
using WorkSpaceManager.Data;
using WorkSpaceManager.Models;

namespace WorkSpaceManager.Services
{
    public static class ProductService
    {
        public static List<Product> GetAllProducts()
        {
            var products = new List<Product>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT * FROM Products WHERE IsActive = 1 ORDER BY Category, Name";
            using var command = new SqliteCommand(query, connection);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                products.Add(new Product
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Category = reader.IsDBNull("Category") ? "" : reader.GetString("Category"),
                    Price = reader.GetDecimal("Price"),
                    Cost = reader.GetDecimal("Cost"),
                    Quantity = reader.GetInt32("Quantity"),
                    Barcode = reader.IsDBNull("Barcode") ? "" : reader.GetString("Barcode"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description")
                });
            }
            
            return products;
        }

        public static Product GetProductById(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT * FROM Products WHERE Id = @id";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            using var reader = command.ExecuteReader();
            
            if (reader.Read())
            {
                return new Product
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Category = reader.IsDBNull("Category") ? "" : reader.GetString("Category"),
                    Price = reader.GetDecimal("Price"),
                    Cost = reader.GetDecimal("Cost"),
                    Quantity = reader.GetInt32("Quantity"),
                    Barcode = reader.IsDBNull("Barcode") ? "" : reader.GetString("Barcode"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description")
                };
            }
            
            return null;
        }

        public static int AddProduct(Product product)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"INSERT INTO Products (Name, Category, Price, Cost, Quantity, Barcode, IsActive, Description) 
                         VALUES (@name, @category, @price, @cost, @quantity, @barcode, @isActive, @description);
                         SELECT last_insert_rowid();";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@name", product.Name);
            command.Parameters.AddWithValue("@category", product.Category ?? "");
            command.Parameters.AddWithValue("@price", product.Price);
            command.Parameters.AddWithValue("@cost", product.Cost);
            command.Parameters.AddWithValue("@quantity", product.Quantity);
            command.Parameters.AddWithValue("@barcode", product.Barcode ?? "");
            command.Parameters.AddWithValue("@isActive", product.IsActive);
            command.Parameters.AddWithValue("@description", product.Description ?? "");
            
            return Convert.ToInt32(command.ExecuteScalar());
        }

        public static bool UpdateProduct(Product product)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"UPDATE Products SET 
                         Name = @name, Category = @category, Price = @price, Cost = @cost, 
                         Quantity = @quantity, Barcode = @barcode, IsActive = @isActive, 
                         Description = @description 
                         WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", product.Id);
            command.Parameters.AddWithValue("@name", product.Name);
            command.Parameters.AddWithValue("@category", product.Category ?? "");
            command.Parameters.AddWithValue("@price", product.Price);
            command.Parameters.AddWithValue("@cost", product.Cost);
            command.Parameters.AddWithValue("@quantity", product.Quantity);
            command.Parameters.AddWithValue("@barcode", product.Barcode ?? "");
            command.Parameters.AddWithValue("@isActive", product.IsActive);
            command.Parameters.AddWithValue("@description", product.Description ?? "");
            
            return command.ExecuteNonQuery() > 0;
        }

        public static bool DeleteProduct(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "UPDATE Products SET IsActive = 0 WHERE Id = @id";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            return command.ExecuteNonQuery() > 0;
        }

        public static List<Product> SearchProducts(string searchTerm)
        {
            var products = new List<Product>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"SELECT * FROM Products 
                         WHERE IsActive = 1 AND (Name LIKE @search OR Category LIKE @search OR Barcode LIKE @search) 
                         ORDER BY Category, Name";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@search", $"%{searchTerm}%");
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                products.Add(new Product
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Category = reader.IsDBNull("Category") ? "" : reader.GetString("Category"),
                    Price = reader.GetDecimal("Price"),
                    Cost = reader.GetDecimal("Cost"),
                    Quantity = reader.GetInt32("Quantity"),
                    Barcode = reader.IsDBNull("Barcode") ? "" : reader.GetString("Barcode"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description")
                });
            }
            
            return products;
        }

        public static List<string> GetCategories()
        {
            var categories = new List<string>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT DISTINCT Category FROM Products WHERE IsActive = 1 AND Category IS NOT NULL AND Category != '' ORDER BY Category";
            using var command = new SqliteCommand(query, connection);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                categories.Add(reader.GetString("Category"));
            }
            
            return categories;
        }

        public static bool UpdateProductQuantity(int productId, int newQuantity)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "UPDATE Products SET Quantity = @quantity WHERE Id = @id";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", productId);
            command.Parameters.AddWithValue("@quantity", newQuantity);
            
            return command.ExecuteNonQuery() > 0;
        }

        public static Product GetProductByBarcode(string barcode)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT * FROM Products WHERE Barcode = @barcode AND IsActive = 1";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@barcode", barcode);
            using var reader = command.ExecuteReader();
            
            if (reader.Read())
            {
                return new Product
                {
                    Id = reader.GetInt32("Id"),
                    Name = reader.GetString("Name"),
                    Category = reader.IsDBNull("Category") ? "" : reader.GetString("Category"),
                    Price = reader.GetDecimal("Price"),
                    Cost = reader.GetDecimal("Cost"),
                    Quantity = reader.GetInt32("Quantity"),
                    Barcode = reader.IsDBNull("Barcode") ? "" : reader.GetString("Barcode"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description")
                };
            }
            
            return null;
        }
    }
}
