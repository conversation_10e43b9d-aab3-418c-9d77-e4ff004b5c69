using System;
using System.Collections.Generic;
using Microsoft.Data.Sqlite;
using WorkSpaceManager.Data;
using WorkSpaceManager.Models;

namespace WorkSpaceManager.Services
{
    public static class PurchaseService
    {
        public static List<Purchase> GetPurchasesBySession(int sessionId)
        {
            var purchases = new List<Purchase>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"SELECT p.*, pr.Name as ProductName 
                         FROM Purchases p 
                         INNER JOIN Products pr ON p.ProductId = pr.Id 
                         WHERE p.SessionId = @sessionId 
                         ORDER BY p.PurchaseTime";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@sessionId", sessionId);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                purchases.Add(new Purchase
                {
                    Id = reader.GetInt32("Id"),
                    SessionId = reader.GetInt32("SessionId"),
                    ProductId = reader.GetInt32("ProductId"),
                    ProductName = reader.GetString("ProductName"),
                    Quantity = reader.GetInt32("Quantity"),
                    UnitPrice = reader.GetDecimal("UnitPrice"),
                    TotalPrice = reader.GetDecimal("TotalPrice"),
                    PurchaseTime = reader.GetDateTime("PurchaseTime"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                });
            }
            
            return purchases;
        }

        public static int AddPurchase(Purchase purchase)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"INSERT INTO Purchases (SessionId, ProductId, Quantity, UnitPrice, TotalPrice, PurchaseTime, Notes) 
                         VALUES (@sessionId, @productId, @quantity, @unitPrice, @totalPrice, @purchaseTime, @notes);
                         SELECT last_insert_rowid();";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@sessionId", purchase.SessionId);
            command.Parameters.AddWithValue("@productId", purchase.ProductId);
            command.Parameters.AddWithValue("@quantity", purchase.Quantity);
            command.Parameters.AddWithValue("@unitPrice", purchase.UnitPrice);
            command.Parameters.AddWithValue("@totalPrice", purchase.TotalPrice);
            command.Parameters.AddWithValue("@purchaseTime", purchase.PurchaseTime);
            command.Parameters.AddWithValue("@notes", purchase.Notes ?? "");
            
            return Convert.ToInt32(command.ExecuteScalar());
        }

        public static bool UpdatePurchase(Purchase purchase)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"UPDATE Purchases SET 
                         ProductId = @productId, 
                         Quantity = @quantity, 
                         UnitPrice = @unitPrice, 
                         TotalPrice = @totalPrice, 
                         Notes = @notes 
                         WHERE Id = @id";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", purchase.Id);
            command.Parameters.AddWithValue("@productId", purchase.ProductId);
            command.Parameters.AddWithValue("@quantity", purchase.Quantity);
            command.Parameters.AddWithValue("@unitPrice", purchase.UnitPrice);
            command.Parameters.AddWithValue("@totalPrice", purchase.TotalPrice);
            command.Parameters.AddWithValue("@notes", purchase.Notes ?? "");
            
            return command.ExecuteNonQuery() > 0;
        }

        public static bool DeletePurchase(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "DELETE FROM Purchases WHERE Id = @id";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            
            return command.ExecuteNonQuery() > 0;
        }

        public static List<Purchase> GetPurchasesByDate(DateTime date)
        {
            var purchases = new List<Purchase>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"SELECT p.*, pr.Name as ProductName 
                         FROM Purchases p 
                         INNER JOIN Products pr ON p.ProductId = pr.Id 
                         WHERE DATE(p.PurchaseTime) = DATE(@date) 
                         ORDER BY p.PurchaseTime";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@date", date.Date);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                purchases.Add(new Purchase
                {
                    Id = reader.GetInt32("Id"),
                    SessionId = reader.GetInt32("SessionId"),
                    ProductId = reader.GetInt32("ProductId"),
                    ProductName = reader.GetString("ProductName"),
                    Quantity = reader.GetInt32("Quantity"),
                    UnitPrice = reader.GetDecimal("UnitPrice"),
                    TotalPrice = reader.GetDecimal("TotalPrice"),
                    PurchaseTime = reader.GetDateTime("PurchaseTime"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes")
                });
            }
            
            return purchases;
        }

        public static decimal GetTotalPurchasesBySession(int sessionId)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT COALESCE(SUM(TotalPrice), 0) FROM Purchases WHERE SessionId = @sessionId";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@sessionId", sessionId);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }

        public static decimal GetTotalPurchasesByDate(DateTime date)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = "SELECT COALESCE(SUM(TotalPrice), 0) FROM Purchases WHERE DATE(PurchaseTime) = DATE(@date)";
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@date", date.Date);
            
            var result = command.ExecuteScalar();
            return result != DBNull.Value ? Convert.ToDecimal(result) : 0;
        }
    }
}
