using System;
using System.Collections.Generic;
using Microsoft.Data.Sqlite;
using WorkSpaceManager.Data;
using WorkSpaceManager.Models;

namespace WorkSpaceManager.Services
{
    public static class SessionService
    {
        public static List<Session> GetActiveSessions()
        {
            var sessions = new List<Session>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"SELECT s.*, c.Name as CustomerName 
                         FROM Sessions s 
                         INNER JOIN Customers c ON s.CustomerId = c.Id 
                         WHERE s.IsActive = 1 AND s.EndTime IS NULL 
                         ORDER BY s.StartTime";
            
            using var command = new SqliteCommand(query, connection);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                sessions.Add(new Session
                {
                    Id = reader.GetInt32("Id"),
                    CustomerId = reader.GetInt32("CustomerId"),
                    CustomerName = reader.GetString("CustomerName"),
                    StartTime = reader.GetDateTime("StartTime"),
                    EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                    HourlyRate = reader.GetDecimal("HourlyRate"),
                    TotalHours = reader.GetDecimal("TotalHours"),
                    TotalAmount = reader.GetDecimal("TotalAmount"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"),
                    ShiftId = reader.IsDBNull("ShiftId") ? 0 : reader.GetInt32("ShiftId")
                });
            }
            
            return sessions;
        }

        public static int StartSession(int customerId, decimal hourlyRate, int shiftId)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"INSERT INTO Sessions (CustomerId, StartTime, HourlyRate, IsActive, ShiftId) 
                         VALUES (@customerId, @startTime, @hourlyRate, 1, @shiftId);
                         SELECT last_insert_rowid();";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@customerId", customerId);
            command.Parameters.AddWithValue("@startTime", DateTime.Now);
            command.Parameters.AddWithValue("@hourlyRate", hourlyRate);
            command.Parameters.AddWithValue("@shiftId", shiftId);
            
            return Convert.ToInt32(command.ExecuteScalar());
        }

        public static bool EndSession(int sessionId)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var endTime = DateTime.Now;
            
            // الحصول على وقت البداية لحساب المدة
            var getStartTimeQuery = "SELECT StartTime, HourlyRate FROM Sessions WHERE Id = @id";
            using var getCommand = new SqliteCommand(getStartTimeQuery, connection);
            getCommand.Parameters.AddWithValue("@id", sessionId);
            using var reader = getCommand.ExecuteReader();
            
            if (reader.Read())
            {
                var startTime = reader.GetDateTime("StartTime");
                var hourlyRate = reader.GetDecimal("HourlyRate");
                reader.Close();
                
                var duration = endTime - startTime;
                var totalHours = (decimal)duration.TotalHours;
                var totalAmount = totalHours * hourlyRate;
                
                var updateQuery = @"UPDATE Sessions SET 
                                   EndTime = @endTime, 
                                   TotalHours = @totalHours, 
                                   TotalAmount = @totalAmount 
                                   WHERE Id = @id";
                
                using var updateCommand = new SqliteCommand(updateQuery, connection);
                updateCommand.Parameters.AddWithValue("@id", sessionId);
                updateCommand.Parameters.AddWithValue("@endTime", endTime);
                updateCommand.Parameters.AddWithValue("@totalHours", totalHours);
                updateCommand.Parameters.AddWithValue("@totalAmount", totalAmount);
                
                return updateCommand.ExecuteNonQuery() > 0;
            }
            
            return false;
        }

        public static Session GetSessionById(int id)
        {
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();

            var query = @"SELECT s.*, c.Name as CustomerName
                         FROM Sessions s
                         INNER JOIN Customers c ON s.CustomerId = c.Id
                         WHERE s.Id = @id";

            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@id", id);
            using var reader = command.ExecuteReader();

            if (reader.Read())
            {
                return new Session
                {
                    Id = reader.GetInt32("Id"),
                    CustomerId = reader.GetInt32("CustomerId"),
                    CustomerName = reader.GetString("CustomerName"),
                    StartTime = reader.GetDateTime("StartTime"),
                    EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                    HourlyRate = reader.GetDecimal("HourlyRate"),
                    TotalHours = reader.GetDecimal("TotalHours"),
                    TotalAmount = reader.GetDecimal("TotalAmount"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"),
                    ShiftId = reader.IsDBNull("ShiftId") ? 0 : reader.GetInt32("ShiftId")
                };
            }

            return null;
        }

        public static List<Session> GetSessionsByCustomer(int customerId)
        {
            var sessions = new List<Session>();

            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();

            var query = @"SELECT s.*, c.Name as CustomerName
                         FROM Sessions s
                         INNER JOIN Customers c ON s.CustomerId = c.Id
                         WHERE s.CustomerId = @customerId
                         ORDER BY s.StartTime DESC";

            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@customerId", customerId);
            using var reader = command.ExecuteReader();

            while (reader.Read())
            {
                sessions.Add(new Session
                {
                    Id = reader.GetInt32("Id"),
                    CustomerId = reader.GetInt32("CustomerId"),
                    CustomerName = reader.GetString("CustomerName"),
                    StartTime = reader.GetDateTime("StartTime"),
                    EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                    HourlyRate = reader.GetDecimal("HourlyRate"),
                    TotalHours = reader.GetDecimal("TotalHours"),
                    TotalAmount = reader.GetDecimal("TotalAmount"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"),
                    ShiftId = reader.IsDBNull("ShiftId") ? 0 : reader.GetInt32("ShiftId")
                });
            }

            return sessions;
        }

        public static List<Session> GetSessionsByDate(DateTime date)
        {
            var sessions = new List<Session>();
            
            using var connection = new SqliteConnection(DatabaseService.ConnectionString);
            connection.Open();
            
            var query = @"SELECT s.*, c.Name as CustomerName 
                         FROM Sessions s 
                         INNER JOIN Customers c ON s.CustomerId = c.Id 
                         WHERE DATE(s.StartTime) = DATE(@date) 
                         ORDER BY s.StartTime";
            
            using var command = new SqliteCommand(query, connection);
            command.Parameters.AddWithValue("@date", date.Date);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                sessions.Add(new Session
                {
                    Id = reader.GetInt32("Id"),
                    CustomerId = reader.GetInt32("CustomerId"),
                    CustomerName = reader.GetString("CustomerName"),
                    StartTime = reader.GetDateTime("StartTime"),
                    EndTime = reader.IsDBNull("EndTime") ? null : reader.GetDateTime("EndTime"),
                    HourlyRate = reader.GetDecimal("HourlyRate"),
                    TotalHours = reader.GetDecimal("TotalHours"),
                    TotalAmount = reader.GetDecimal("TotalAmount"),
                    IsActive = reader.GetBoolean("IsActive"),
                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"),
                    ShiftId = reader.IsDBNull("ShiftId") ? 0 : reader.GetInt32("ShiftId")
                });
            }
            
            return sessions;
        }
    }
}
