using System;
using System.Windows;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class AddCustomerWindow : Window
    {
        public AddCustomerWindow()
        {
            InitializeComponent();
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(TxtCustomerName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtCustomerName.Focus();
                    return;
                }

                // إنشاء عميل جديد
                var customer = new Customer
                {
                    Name = TxtCustomerName.Text.Trim(),
                    Phone = TxtPhone.Text.Trim(),
                    Email = TxtEmail.Text.Trim(),
                    RegistrationDate = DateTime.Now,
                    IsActive = true,
                    Notes = TxtNotes.Text.Trim()
                };

                // حفظ العميل
                var customerId = CustomerService.AddCustomer(customer);
                
                if (customerId > 0)
                {
                    MessageBox.Show("تم إضافة العميل بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة العميل", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العميل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
