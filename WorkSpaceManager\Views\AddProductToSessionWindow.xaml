<Window x:Class="WorkSpaceManager.Views.AddProductToSessionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة منتج للجلسة" 
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="إضافة منتج للجلسة" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"
                   HorizontalAlignment="Center"/>

        <!-- النموذج -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- معلومات المنتج -->
            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="معلومات المنتج" 
                         FontWeight="Bold" 
                         FontSize="16" 
                         Margin="0,0,0,15"/>

                <Border Background="#F5F5F5" 
                        CornerRadius="5" 
                        Padding="15" 
                        Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock x:Name="TxtProductName" 
                                 Text="اسم المنتج" 
                                 FontSize="16" 
                                 FontWeight="Bold"/>
                        
                        <TextBlock x:Name="TxtProductCategory" 
                                 Text="الفئة" 
                                 FontSize="12" 
                                 Foreground="Gray"
                                 Margin="0,5,0,0"/>
                        
                        <TextBlock x:Name="TxtProductPrice" 
                                 Text="السعر: 0.00 ريال" 
                                 FontSize="14" 
                                 FontWeight="Bold"
                                 Foreground="#4CAF50"
                                 Margin="0,10,0,0"/>
                        
                        <TextBlock x:Name="TxtProductQuantity" 
                                 Text="الكمية المتاحة: 0" 
                                 FontSize="12" 
                                 Foreground="Gray"
                                 Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- اختيار الجلسة -->
                <TextBlock Text="اختيار الجلسة:" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CmbSessions" 
                          Height="35"
                          Margin="0,0,0,15"
                          SelectionChanged="CmbSessions_SelectionChanged">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <StackPanel>
                                <TextBlock Text="{Binding CustomerName}" FontWeight="Bold"/>
                                <TextBlock Text="{Binding StartTime, StringFormat='بدأ في: {0:HH:mm}'}" 
                                         FontSize="10" Foreground="Gray"/>
                            </StackPanel>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- الكمية -->
                <TextBlock Text="الكمية:" FontWeight="Bold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <Button Grid.Column="0" 
                            Content="−" 
                            Width="30" 
                            Height="30"
                            Background="#F44336"
                            Foreground="White"
                            Click="BtnDecrease_Click"/>
                    
                    <TextBox x:Name="TxtQuantity" 
                             Grid.Column="1"
                             Text="1"
                             Height="30"
                             VerticalContentAlignment="Center"
                             HorizontalContentAlignment="Center"
                             FontSize="16"
                             FontWeight="Bold"
                             Margin="5,0"
                             TextChanged="TxtQuantity_TextChanged"/>
                    
                    <Button Grid.Column="2" 
                            Content="+" 
                            Width="30" 
                            Height="30"
                            Background="#4CAF50"
                            Foreground="White"
                            Click="BtnIncrease_Click"/>
                </Grid>

                <!-- السعر الإجمالي -->
                <TextBlock Text="السعر الإجمالي:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtTotalPrice" 
                         IsReadOnly="True"
                         Background="#E8F5E8"
                         Height="35"
                         VerticalContentAlignment="Center"
                         FontSize="18"
                         FontWeight="Bold"
                         Foreground="#4CAF50"
                         Margin="0,0,0,15"/>
            </StackPanel>

            <!-- معلومات الجلسة -->
            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <TextBlock Text="معلومات الجلسة المحددة" 
                         FontWeight="Bold" 
                         FontSize="16" 
                         Margin="0,0,0,15"/>

                <Border Background="#E3F2FD" 
                        CornerRadius="5" 
                        Padding="15" 
                        Margin="0,0,0,20">
                    <StackPanel x:Name="SessionInfo">
                        <TextBlock Text="يرجى اختيار جلسة" 
                                 FontStyle="Italic" 
                                 Foreground="Gray"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- ملاحظات -->
                <TextBlock Text="ملاحظات (اختياري):" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtNotes" 
                         Height="100"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"/>
            </StackPanel>
        </Grid>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            
            <Button x:Name="BtnAdd" 
                    Content="🛒 إضافة للجلسة" 
                    Background="#4CAF50" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    IsEnabled="False"
                    Click="BtnAdd_Click"/>
            
            <Button x:Name="BtnCancel" 
                    Content="إلغاء" 
                    Background="#F44336" 
                    Foreground="White" 
                    Padding="20,10"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
