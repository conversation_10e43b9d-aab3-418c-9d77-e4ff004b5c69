using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class AddProductToSessionWindow : Window
    {
        private readonly Product _product;
        private Session _selectedSession;

        public AddProductToSessionWindow(Product product)
        {
            InitializeComponent();
            _product = product;
            LoadProductInfo();
            LoadActiveSessions();
        }

        private void LoadProductInfo()
        {
            if (_product != null)
            {
                TxtProductName.Text = _product.Name;
                TxtProductCategory.Text = string.IsNullOrEmpty(_product.Category) ? "غير محدد" : _product.Category;
                TxtProductPrice.Text = $"السعر: {_product.Price:F2} ريال";
                TxtProductQuantity.Text = $"الكمية المتاحة: {_product.Quantity}";
                
                UpdateTotalPrice();
            }
        }

        private void LoadActiveSessions()
        {
            try
            {
                var activeSessions = SessionService.GetActiveSessions();
                CmbSessions.ItemsSource = activeSessions;
                
                if (activeSessions.Any())
                {
                    CmbSessions.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الجلسات النشطة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbSessions_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedSession = CmbSessions.SelectedItem as Session;
            UpdateSessionInfo();
            BtnAdd.IsEnabled = _selectedSession != null;
        }

        private void UpdateSessionInfo()
        {
            SessionInfo.Children.Clear();

            if (_selectedSession != null)
            {
                var customerInfo = new TextBlock
                {
                    Text = $"العميل: {_selectedSession.CustomerName}",
                    FontWeight = FontWeights.Bold,
                    FontSize = 14
                };
                SessionInfo.Children.Add(customerInfo);

                var startTime = new TextBlock
                {
                    Text = $"بدأ في: {_selectedSession.StartTime:yyyy-MM-dd HH:mm}",
                    FontSize = 12,
                    Margin = new Thickness(0, 5, 0, 0)
                };
                SessionInfo.Children.Add(startTime);

                var duration = DateTime.Now - _selectedSession.StartTime;
                var durationInfo = new TextBlock
                {
                    Text = $"المدة: {duration.Hours:00}:{duration.Minutes:00}",
                    FontSize = 12,
                    Margin = new Thickness(0, 5, 0, 0)
                };
                SessionInfo.Children.Add(durationInfo);

                var hourlyRate = new TextBlock
                {
                    Text = $"سعر الساعة: {_selectedSession.HourlyRate:F2} ريال",
                    FontSize = 12,
                    Margin = new Thickness(0, 5, 0, 0)
                };
                SessionInfo.Children.Add(hourlyRate);
            }
            else
            {
                var noSelection = new TextBlock
                {
                    Text = "يرجى اختيار جلسة",
                    FontStyle = FontStyles.Italic,
                    Foreground = System.Windows.Media.Brushes.Gray,
                    HorizontalAlignment = HorizontalAlignment.Center
                };
                SessionInfo.Children.Add(noSelection);
            }
        }

        private void BtnDecrease_Click(object sender, RoutedEventArgs e)
        {
            if (int.TryParse(TxtQuantity.Text, out int quantity) && quantity > 1)
            {
                TxtQuantity.Text = (quantity - 1).ToString();
            }
        }

        private void BtnIncrease_Click(object sender, RoutedEventArgs e)
        {
            if (int.TryParse(TxtQuantity.Text, out int quantity))
            {
                if (quantity < _product.Quantity)
                {
                    TxtQuantity.Text = (quantity + 1).ToString();
                }
                else
                {
                    MessageBox.Show($"الكمية المتاحة هي {_product.Quantity} فقط", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
        }

        private void TxtQuantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateTotalPrice();
        }

        private void UpdateTotalPrice()
        {
            if (int.TryParse(TxtQuantity.Text, out int quantity) && _product != null)
            {
                var totalPrice = quantity * _product.Price;
                TxtTotalPrice.Text = $"{totalPrice:F2} ريال";
            }
            else
            {
                TxtTotalPrice.Text = "0.00 ريال";
            }
        }

        private void BtnAdd_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (_selectedSession == null)
                {
                    MessageBox.Show("يرجى اختيار جلسة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!int.TryParse(TxtQuantity.Text, out int quantity) || quantity <= 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtQuantity.Focus();
                    return;
                }

                if (quantity > _product.Quantity)
                {
                    MessageBox.Show($"الكمية المطلوبة ({quantity}) أكبر من الكمية المتاحة ({_product.Quantity})", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtQuantity.Focus();
                    return;
                }

                // إنشاء عملية شراء جديدة
                var purchase = new Purchase
                {
                    SessionId = _selectedSession.Id,
                    ProductId = _product.Id,
                    Quantity = quantity,
                    UnitPrice = _product.Price,
                    TotalPrice = quantity * _product.Price,
                    PurchaseTime = DateTime.Now,
                    Notes = TxtNotes.Text.Trim()
                };

                // حفظ عملية الشراء
                var purchaseId = PurchaseService.AddPurchase(purchase);
                
                if (purchaseId > 0)
                {
                    // تحديث كمية المنتج
                    var newQuantity = _product.Quantity - quantity;
                    ProductService.UpdateProductQuantity(_product.Id, newQuantity);

                    MessageBox.Show($"تم إضافة المنتج للجلسة بنجاح\n" +
                                   $"المنتج: {_product.Name}\n" +
                                   $"الكمية: {quantity}\n" +
                                   $"الإجمالي: {purchase.TotalPrice:F2} ريال", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة المنتج للجلسة", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج للجلسة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
