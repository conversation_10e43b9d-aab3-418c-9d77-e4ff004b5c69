using System;
using System.Windows;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class AddProductWindow : Window
    {
        public AddProductWindow()
        {
            InitializeComponent();
            LoadCategories();
        }

        private void LoadCategories()
        {
            try
            {
                var categories = ProductService.GetCategories();
                CmbCategory.ItemsSource = categories;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnGenerateBarcode_Click(object sender, RoutedEventArgs e)
        {
            // توليد باركود عشوائي
            var random = new Random();
            var barcode = "";
            for (int i = 0; i < 13; i++)
            {
                barcode += random.Next(0, 10).ToString();
            }
            TxtBarcode.Text = barcode;
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(TxtProductName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtProductName.Focus();
                    return;
                }

                if (!decimal.TryParse(TxtPrice.Text, out decimal price) || price < 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtPrice.Focus();
                    return;
                }

                if (!decimal.TryParse(TxtCost.Text, out decimal cost) || cost < 0)
                {
                    MessageBox.Show("يرجى إدخال تكلفة صحيحة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtCost.Focus();
                    return;
                }

                if (!int.TryParse(TxtQuantity.Text, out int quantity) || quantity < 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtQuantity.Focus();
                    return;
                }

                // التحقق من عدم تكرار الباركود
                if (!string.IsNullOrWhiteSpace(TxtBarcode.Text))
                {
                    var existingProduct = ProductService.GetProductByBarcode(TxtBarcode.Text.Trim());
                    if (existingProduct != null)
                    {
                        MessageBox.Show("هذا الباركود مستخدم بالفعل لمنتج آخر", "تنبيه", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        TxtBarcode.Focus();
                        return;
                    }
                }

                // إنشاء منتج جديد
                var product = new Product
                {
                    Name = TxtProductName.Text.Trim(),
                    Category = CmbCategory.Text.Trim(),
                    Price = price,
                    Cost = cost,
                    Quantity = quantity,
                    Barcode = TxtBarcode.Text.Trim(),
                    IsActive = ChkIsActive.IsChecked ?? true,
                    Description = TxtDescription.Text.Trim()
                };

                // حفظ المنتج
                var productId = ProductService.AddProduct(product);
                
                if (productId > 0)
                {
                    MessageBox.Show("تم إضافة المنتج بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة المنتج", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
