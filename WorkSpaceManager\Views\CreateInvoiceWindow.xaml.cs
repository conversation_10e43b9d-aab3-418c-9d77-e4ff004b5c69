using System;
using System.Linq;
using System.Windows;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class CreateInvoiceWindow : Window
    {
        private Session _selectedSession;

        public CreateInvoiceWindow()
        {
            InitializeComponent();
            LoadActiveSessions();
        }

        private void LoadActiveSessions()
        {
            try
            {
                var sessions = SessionService.GetActiveSessions();
                CmbSessions.ItemsSource = sessions;
                
                if (sessions.Any())
                {
                    CmbSessions.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الجلسات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbSessions_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            if (CmbSessions.SelectedItem is Session session)
            {
                _selectedSession = session;
                LoadInvoiceDetails();
            }
        }

        private void LoadInvoiceDetails()
        {
            if (_selectedSession == null) return;

            try
            {
                // معلومات العميل
                var customer = CustomerService.GetCustomerById(_selectedSession.CustomerId);
                if (customer != null)
                {
                    TxtCustomerName.Text = $"اسم العميل: {customer.Name}";
                    TxtCustomerPhone.Text = $"الهاتف: {customer.Phone}";
                }

                // معلومات الجلسة
                TxtSessionStart.Text = $"وقت البداية: {_selectedSession.StartTime:yyyy-MM-dd HH:mm}";
                
                if (_selectedSession.EndTime.HasValue)
                {
                    TxtSessionEnd.Text = $"وقت النهاية: {_selectedSession.EndTime:yyyy-MM-dd HH:mm}";
                    var duration = _selectedSession.EndTime.Value - _selectedSession.StartTime;
                    TxtSessionDuration.Text = $"المدة: {duration.Hours:00}:{duration.Minutes:00}";
                }
                else
                {
                    TxtSessionEnd.Text = "وقت النهاية: جلسة نشطة";
                    var duration = DateTime.Now - _selectedSession.StartTime;
                    TxtSessionDuration.Text = $"المدة: {duration.Hours:00}:{duration.Minutes:00}";
                }

                // تفاصيل الوقت
                TxtHourlyRate.Text = $"{_selectedSession.HourlyRate:F2} ريال";
                TxtTotalHours.Text = $"{_selectedSession.TotalHours:F2} ساعة";
                TxtTimeTotal.Text = $"{_selectedSession.TotalAmount:F2} ريال";

                // المشتريات
                LoadPurchases();
                
                // الإجمالي النهائي
                CalculateGrandTotal();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل الفاتورة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadPurchases()
        {
            try
            {
                var purchases = PurchaseService.GetPurchasesBySession(_selectedSession.Id);
                DgPurchases.ItemsSource = purchases;
                
                var purchasesTotal = purchases.Sum(p => p.TotalPrice);
                TxtPurchasesTotal.Text = $"إجمالي المشتريات: {purchasesTotal:F2} ريال";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المشتريات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CalculateGrandTotal()
        {
            var timeTotal = _selectedSession.TotalAmount;
            var purchasesTotal = DgPurchases.Items.Cast<Purchase>().Sum(p => p.TotalPrice);
            var grandTotal = timeTotal + purchasesTotal;

            TxtFinalTimeTotal.Text = $"{timeTotal:F2} ريال";
            TxtFinalPurchasesTotal.Text = $"{purchasesTotal:F2} ريال";
            TxtGrandTotal.Text = $"{grandTotal:F2} ريال";
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadActiveSessions();
        }

        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedSession == null)
            {
                MessageBox.Show("يرجى اختيار جلسة أولاً", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                PrintService.PrintInvoice(_selectedSession);
                MessageBox.Show("تم إرسال الفاتورة للطباعة", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedSession == null)
            {
                MessageBox.Show("يرجى اختيار جلسة أولاً", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                InvoiceService.SaveInvoice(_selectedSession);
                MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
