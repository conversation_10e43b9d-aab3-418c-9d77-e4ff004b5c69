using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class CustomerHistoryWindow : Window
    {
        private readonly Customer _customer;
        private List<Session> _allSessions;
        private List<Purchase> _allPurchases;

        public CustomerHistoryWindow(Customer customer)
        {
            InitializeComponent();
            _customer = customer;
            LoadCustomerInfo();
            LoadCustomerHistory();
        }

        private void LoadCustomerInfo()
        {
            TxtCustomerName.Text = _customer.Name;
            TxtCustomerPhone.Text = string.IsNullOrEmpty(_customer.Phone) ? "غير محدد" : _customer.Phone;
        }

        private void LoadCustomerHistory()
        {
            try
            {
                // تحميل جلسات العميل
                _allSessions = GetCustomerSessions();
                DgSessions.ItemsSource = _allSessions;

                // تحميل مشتريات العميل
                _allPurchases = GetCustomerPurchases();
                DgPurchases.ItemsSource = _allPurchases;

                // حساب الإحصائيات
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تاريخ العميل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<Session> GetCustomerSessions()
        {
            // هذه دالة مساعدة - يجب إضافتها لـ SessionService
            return SessionService.GetSessionsByCustomer(_customer.Id);
        }

        private List<Purchase> GetCustomerPurchases()
        {
            var purchases = new List<Purchase>();
            foreach (var session in _allSessions)
            {
                var sessionPurchases = PurchaseService.GetPurchasesBySession(session.Id);
                purchases.AddRange(sessionPurchases);
            }
            return purchases;
        }

        private void UpdateStatistics()
        {
            var totalSessions = _allSessions.Count;
            var totalHours = _allSessions.Sum(s => s.TotalHours);
            var totalSessionAmount = _allSessions.Sum(s => s.TotalAmount);
            var totalPurchaseAmount = _allPurchases.Sum(p => p.TotalPrice);
            var totalAmount = totalSessionAmount + totalPurchaseAmount;

            TxtTotalSessions.Text = $"إجمالي الجلسات: {totalSessions}";
            TxtTotalHours.Text = $"إجمالي الساعات: {totalHours:F2}";
            TxtTotalAmount.Text = $"إجمالي المبلغ: {totalAmount:F2} ريال";
        }

        private void DateFilter_Changed(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            // يمكن تطبيق الفلتر تلقائياً عند تغيير التاريخ
        }

        private void BtnFilter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filteredSessions = _allSessions.AsEnumerable();
                var filteredPurchases = _allPurchases.AsEnumerable();

                if (DpFromDate.SelectedDate.HasValue)
                {
                    var fromDate = DpFromDate.SelectedDate.Value.Date;
                    filteredSessions = filteredSessions.Where(s => s.StartTime.Date >= fromDate);
                    filteredPurchases = filteredPurchases.Where(p => p.PurchaseTime.Date >= fromDate);
                }

                if (DpToDate.SelectedDate.HasValue)
                {
                    var toDate = DpToDate.SelectedDate.Value.Date.AddDays(1);
                    filteredSessions = filteredSessions.Where(s => s.StartTime.Date < toDate);
                    filteredPurchases = filteredPurchases.Where(p => p.PurchaseTime.Date < toDate);
                }

                DgSessions.ItemsSource = filteredSessions.ToList();
                DgPurchases.ItemsSource = filteredPurchases.ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلتر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnReset_Click(object sender, RoutedEventArgs e)
        {
            DpFromDate.SelectedDate = null;
            DpToDate.SelectedDate = null;
            DgSessions.ItemsSource = _allSessions;
            DgPurchases.ItemsSource = _allPurchases;
        }

        private void BtnDetailedReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportWindow = new CustomerReportWindow(_customer, _allSessions, _allPurchases);
                reportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PrintService.PrintCustomerHistory(_customer, _allSessions, _allPurchases);
                MessageBox.Show("تم إرسال التقرير للطباعة", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    // Converters for Session Status
    public class SessionStatusToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isActive)
            {
                return isActive ? new SolidColorBrush(Colors.Orange) : new SolidColorBrush(Colors.Green);
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class SessionStatusToTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isActive)
            {
                return isActive ? "نشطة" : "مكتملة";
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
