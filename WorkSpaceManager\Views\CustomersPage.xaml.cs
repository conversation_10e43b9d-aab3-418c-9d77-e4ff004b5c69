using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class CustomersPage : Page
    {
        private List<Customer> _allCustomers;
        private Customer _selectedCustomer;

        public CustomersPage()
        {
            InitializeComponent();
            LoadCustomers();
        }

        private void LoadCustomers()
        {
            try
            {
                _allCustomers = CustomerService.GetAllCustomers();
                DgCustomers.ItemsSource = _allCustomers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة العملاء: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterCustomers();
        }

        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            FilterCustomers();
        }

        private void FilterCustomers()
        {
            if (_allCustomers == null) return;

            var searchTerm = TxtSearch.Text.Trim().ToLower();
            
            if (string.IsNullOrEmpty(searchTerm))
            {
                DgCustomers.ItemsSource = _allCustomers;
            }
            else
            {
                var filteredCustomers = _allCustomers.Where(c =>
                    c.Name.ToLower().Contains(searchTerm) ||
                    (c.Phone != null && c.Phone.Contains(searchTerm)) ||
                    (c.Email != null && c.Email.ToLower().Contains(searchTerm))
                ).ToList();

                DgCustomers.ItemsSource = filteredCustomers;
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadCustomers();
            TxtSearch.Text = "";
        }

        private void BtnAddCustomer_Click(object sender, RoutedEventArgs e)
        {
            var addCustomerWindow = new AddCustomerWindow();
            if (addCustomerWindow.ShowDialog() == true)
            {
                LoadCustomers();
            }
        }

        private void BtnEditCustomer_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editCustomerWindow = new EditCustomerWindow(_selectedCustomer);
            if (editCustomerWindow.ShowDialog() == true)
            {
                LoadCustomers();
            }
        }

        private void BtnDeleteCustomer_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedCustomer == null)
            {
                MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف العميل '{_selectedCustomer.Name}'؟\nهذا الإجراء لا يمكن التراجع عنه.", 
                "تأكيد الحذف", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    if (CustomerService.DeleteCustomer(_selectedCustomer.Id))
                    {
                        MessageBox.Show("تم حذف العميل بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadCustomers();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف العميل", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DgCustomers_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedCustomer = DgCustomers.SelectedItem as Customer;
            
            BtnEditCustomer.IsEnabled = _selectedCustomer != null;
            BtnDeleteCustomer.IsEnabled = _selectedCustomer != null;
        }

        private void DgCustomers_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (_selectedCustomer != null)
            {
                BtnEditCustomer_Click(sender, e);
            }
        }

        private void BtnStartSession_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.Tag is int customerId)
            {
                var customer = _allCustomers.FirstOrDefault(c => c.Id == customerId);
                if (customer != null)
                {
                    var startSessionWindow = new StartSessionWindow();
                    // يمكن تمرير معرف العميل للنافذة هنا
                    startSessionWindow.ShowDialog();
                }
            }
        }

        private void BtnViewHistory_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.Tag is int customerId)
            {
                var customer = _allCustomers.FirstOrDefault(c => c.Id == customerId);
                if (customer != null)
                {
                    var historyWindow = new CustomerHistoryWindow(customer);
                    historyWindow.ShowDialog();
                }
            }
        }
    }

    // Converters
    public class BoolToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isActive)
            {
                return isActive ? new SolidColorBrush(Colors.Green) : new SolidColorBrush(Colors.Red);
            }
            return new SolidColorBrush(Colors.Gray);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BoolToStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isActive)
            {
                return isActive ? "نشط" : "غير نشط";
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
