<Page x:Class="WorkSpaceManager.Views.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      Title="الرئيسية"
      FlowDirection="RightToLeft">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <TextBlock Text="لوحة التحكم الرئيسية" 
                     FontSize="28" FontWeight="Bold" 
                     Foreground="#2196F3" Margin="0,0,0,20"/>

            <TextBlock Text="مرحباً بك في نظام إدارة المساحات المشتركة" 
                     FontSize="16" Margin="0,0,0,20"/>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Border Grid.Column="0" Background="#E3F2FD" 
                      CornerRadius="5" Padding="20" Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="الإجراءات السريعة" 
                                 FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>
                        <Button x:Name="BtnStartSession" Content="🚀 بدء جلسة جديدة" 
                              Margin="0,5" Padding="15,10" Background="#4CAF50" Foreground="White"
                              Click="BtnStartSession_Click"/>
                        <Button x:Name="BtnNewCustomer" Content="👤 إضافة عميل جديد" 
                              Margin="0,5" Padding="15,10" Background="#2196F3" Foreground="White"
                              Click="BtnNewCustomer_Click"/>
                    </StackPanel>
                </Border>

                <Border Grid.Column="1" Background="#F5F5F5" 
                      CornerRadius="5" Padding="20" Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="إحصائيات سريعة" 
                                 FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/>
                        <TextBlock x:Name="TxtActiveSessions" Text="الجلسات النشطة: 0"/>
                        <TextBlock x:Name="TxtTodaySales" Text="مبيعات اليوم: 0.00 ريال"/>
                    </StackPanel>
                </Border>
            </Grid>
        </StackPanel>
    </ScrollViewer>
</Page>
