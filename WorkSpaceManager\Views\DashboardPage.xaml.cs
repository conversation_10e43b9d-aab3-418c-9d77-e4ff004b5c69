using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class DashboardPage : Page
    {
        public DashboardPage()
        {
            InitializeComponent();
            LoadDashboardData();
            
            // تحديث البيانات كل 30 ثانية
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(30);
            timer.Tick += (s, e) => LoadDashboardData();
            timer.Start();
        }

        private void LoadDashboardData()
        {
            try
            {
                LoadStatistics();
                LoadActiveSessions();
                LoadRecentActivities();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadStatistics()
        {
            // الجلسات النشطة
            var activeSessions = SessionService.GetActiveSessions();
            TxtActiveSessions.Text = activeSessions.Count.ToString();

            // مبيعات اليوم
            var todaySales = ReportService.GetTodaySales();
            TxtTodaySales.Text = todaySales.ToString("F2");

            // إجمالي العملاء
            var totalCustomers = CustomerService.GetAllCustomers().Count;
            TxtTotalCustomers.Text = totalCustomers.ToString();

            // أرباح الشهر
            var monthlyProfit = ReportService.GetMonthlyProfit(DateTime.Now);
            TxtMonthlyProfit.Text = monthlyProfit.ToString("F2");
        }

        private void LoadActiveSessions()
        {
            var activeSessions = SessionService.GetActiveSessions();
            LvActiveSessions.ItemsSource = activeSessions;
        }

        private void LoadRecentActivities()
        {
            var activities = new List<RecentActivity>();

            // إضافة الجلسات الأخيرة
            var recentSessions = SessionService.GetSessionsByDate(DateTime.Today)
                .OrderByDescending(s => s.StartTime)
                .Take(5);

            foreach (var session in recentSessions)
            {
                activities.Add(new RecentActivity
                {
                    Icon = "⏰",
                    Description = $"بدء جلسة للعميل {session.CustomerName}",
                    Details = $"السعر: {session.HourlyRate:F2} ريال/ساعة",
                    Time = session.StartTime
                });
            }

            // إضافة العملاء الجدد
            var newCustomers = CustomerService.GetAllCustomers()
                .Where(c => c.RegistrationDate.Date == DateTime.Today)
                .OrderByDescending(c => c.RegistrationDate)
                .Take(3);

            foreach (var customer in newCustomers)
            {
                activities.Add(new RecentActivity
                {
                    Icon = "👤",
                    Description = $"تسجيل عميل جديد: {customer.Name}",
                    Details = customer.Phone,
                    Time = customer.RegistrationDate
                });
            }

            LvRecentActivities.ItemsSource = activities.OrderByDescending(a => a.Time).Take(10);
        }

        private void BtnStartSession_Click(object sender, RoutedEventArgs e)
        {
            var startSessionWindow = new StartSessionWindow();
            if (startSessionWindow.ShowDialog() == true)
            {
                LoadDashboardData();
            }
        }

        private void BtnNewCustomer_Click(object sender, RoutedEventArgs e)
        {
            var addCustomerWindow = new AddCustomerWindow();
            if (addCustomerWindow.ShowDialog() == true)
            {
                LoadDashboardData();
            }
        }

        private void BtnStartShift_Click(object sender, RoutedEventArgs e)
        {
            var startShiftWindow = new StartShiftWindow();
            if (startShiftWindow.ShowDialog() == true)
            {
                LoadDashboardData();
            }
        }

        private void BtnCreateInvoice_Click(object sender, RoutedEventArgs e)
        {
            var createInvoiceWindow = new CreateInvoiceWindow();
            createInvoiceWindow.ShowDialog();
        }

        private void BtnEndSession_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.Tag is int sessionId)
            {
                var result = MessageBox.Show("هل أنت متأكد من إنهاء هذه الجلسة؟", "تأكيد", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    if (SessionService.EndSession(sessionId))
                    {
                        MessageBox.Show("تم إنهاء الجلسة بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadDashboardData();
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنهاء الجلسة", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }

    public class RecentActivity
    {
        public string Icon { get; set; }
        public string Description { get; set; }
        public string Details { get; set; }
        public DateTime Time { get; set; }
    }
}
