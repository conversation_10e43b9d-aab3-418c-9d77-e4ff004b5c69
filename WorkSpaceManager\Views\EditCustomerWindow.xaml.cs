using System;
using System.Windows;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class EditCustomerWindow : Window
    {
        private readonly Customer _customer;

        public EditCustomerWindow(Customer customer)
        {
            InitializeComponent();
            _customer = customer;
            LoadCustomerData();
        }

        private void LoadCustomerData()
        {
            if (_customer != null)
            {
                TxtCustomerId.Text = _customer.Id.ToString();
                TxtCustomerName.Text = _customer.Name;
                TxtPhone.Text = _customer.Phone;
                TxtEmail.Text = _customer.Email;
                TxtRegistrationDate.Text = _customer.RegistrationDate.ToString("yyyy-MM-dd HH:mm");
                ChkIsActive.IsChecked = _customer.IsActive;
                TxtNotes.Text = _customer.Notes;
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(TxtCustomerName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtCustomerName.Focus();
                    return;
                }

                // تحديث بيانات العميل
                _customer.Name = TxtCustomerName.Text.Trim();
                _customer.Phone = TxtPhone.Text.Trim();
                _customer.Email = TxtEmail.Text.Trim();
                _customer.IsActive = ChkIsActive.IsChecked ?? true;
                _customer.Notes = TxtNotes.Text.Trim();

                // حفظ التغييرات
                if (CustomerService.UpdateCustomer(_customer))
                {
                    MessageBox.Show("تم حفظ التغييرات بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التغييرات", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
