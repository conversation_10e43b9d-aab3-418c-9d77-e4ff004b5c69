<Window x:Class="WorkSpaceManager.Views.EditProductWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل بيانات المنتج" 
        Height="650" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="تعديل بيانات المنتج" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"
                   HorizontalAlignment="Center"/>

        <!-- النموذج -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,0,0,20">
                
                <!-- معرف المنتج -->
                <TextBlock Text="معرف المنتج:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtProductId" 
                         IsReadOnly="True"
                         Background="#F5F5F5"
                         Margin="0,0,0,15"
                         Height="35"
                         VerticalContentAlignment="Center"/>

                <!-- اسم المنتج -->
                <TextBlock Text="اسم المنتج: *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtProductName" 
                         Margin="0,0,0,15"
                         Height="35"
                         VerticalContentAlignment="Center"/>

                <!-- الفئة -->
                <TextBlock Text="الفئة:" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CmbCategory" 
                          IsEditable="True"
                          Margin="0,0,0,15"
                          Height="35"/>

                <!-- السعر -->
                <TextBlock Text="السعر (ريال): *" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtPrice" 
                         Margin="0,0,0,15"
                         Height="35"
                         VerticalContentAlignment="Center"/>

                <!-- التكلفة -->
                <TextBlock Text="التكلفة (ريال):" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtCost" 
                         Margin="0,0,0,15"
                         Height="35"
                         VerticalContentAlignment="Center"/>

                <!-- الكمية الحالية -->
                <TextBlock Text="الكمية الحالية:" FontWeight="Bold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="TxtQuantity" 
                             Grid.Column="0"
                             Height="35"
                             VerticalContentAlignment="Center"/>
                    
                    <Button Grid.Column="1" 
                            Content="📦 تحديث الكمية"
                            Margin="10,0,0,0"
                            Padding="10,5"
                            Background="#FF9800"
                            Foreground="White"
                            Click="BtnUpdateQuantity_Click"/>
                </Grid>

                <!-- الباركود -->
                <TextBlock Text="الباركود:" FontWeight="Bold" Margin="0,0,0,5"/>
                <Grid Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="TxtBarcode" 
                             Grid.Column="0"
                             Height="35"
                             VerticalContentAlignment="Center"/>
                    
                    <Button Grid.Column="1" 
                            Content="🎲 توليد جديد"
                            Margin="10,0,0,0"
                            Padding="10,5"
                            Background="#9E9E9E"
                            Foreground="White"
                            Click="BtnGenerateBarcode_Click"/>
                </Grid>

                <!-- حالة المنتج -->
                <CheckBox x:Name="ChkIsActive" 
                          Content="المنتج نشط" 
                          FontWeight="Bold"
                          Margin="0,0,0,15"/>

                <!-- الوصف -->
                <TextBlock Text="الوصف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtDescription" 
                         Height="80"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,15"/>

                <!-- إحصائيات المنتج -->
                <Border Background="#E3F2FD" 
                        CornerRadius="5" 
                        Padding="15">
                    <StackPanel>
                        <TextBlock Text="إحصائيات المنتج" 
                                 FontWeight="Bold" 
                                 FontSize="14" 
                                 Margin="0,0,0,10"/>
                        
                        <TextBlock x:Name="TxtProfitMargin" 
                                 Text="هامش الربح: 0.00 ريال"/>
                        
                        <TextBlock x:Name="TxtProfitPercentage" 
                                 Text="نسبة الربح: 0.00%"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            
            <Button x:Name="BtnSave" 
                    Content="💾 حفظ التغييرات" 
                    Background="#4CAF50" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    Click="BtnSave_Click"/>
            
            <Button x:Name="BtnCancel" 
                    Content="إلغاء" 
                    Background="#F44336" 
                    Foreground="White" 
                    Padding="20,10"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
