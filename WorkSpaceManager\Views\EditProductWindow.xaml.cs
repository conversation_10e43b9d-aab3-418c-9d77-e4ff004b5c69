using System;
using System.Windows;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class EditProductWindow : Window
    {
        private readonly Product _product;

        public EditProductWindow(Product product)
        {
            InitializeComponent();
            _product = product;
            LoadCategories();
            LoadProductData();
        }

        private void LoadCategories()
        {
            try
            {
                var categories = ProductService.GetCategories();
                CmbCategory.ItemsSource = categories;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadProductData()
        {
            if (_product != null)
            {
                TxtProductId.Text = _product.Id.ToString();
                TxtProductName.Text = _product.Name;
                CmbCategory.Text = _product.Category;
                TxtPrice.Text = _product.Price.ToString("F2");
                TxtCost.Text = _product.Cost.ToString("F2");
                TxtQuantity.Text = _product.Quantity.ToString();
                TxtBarcode.Text = _product.Barcode;
                ChkIsActive.IsChecked = _product.IsActive;
                TxtDescription.Text = _product.Description;

                UpdateStatistics();
            }
        }

        private void UpdateStatistics()
        {
            if (decimal.TryParse(TxtPrice.Text, out decimal price) && 
                decimal.TryParse(TxtCost.Text, out decimal cost))
            {
                var profitMargin = price - cost;
                var profitPercentage = cost > 0 ? (profitMargin / cost) * 100 : 0;

                TxtProfitMargin.Text = $"هامش الربح: {profitMargin:F2} ريال";
                TxtProfitPercentage.Text = $"نسبة الربح: {profitPercentage:F2}%";
            }
        }

        private void BtnGenerateBarcode_Click(object sender, RoutedEventArgs e)
        {
            var random = new Random();
            var barcode = "";
            for (int i = 0; i < 13; i++)
            {
                barcode += random.Next(0, 10).ToString();
            }
            TxtBarcode.Text = barcode;
        }

        private void BtnUpdateQuantity_Click(object sender, RoutedEventArgs e)
        {
            var updateQuantityWindow = new UpdateQuantityWindow(_product);
            if (updateQuantityWindow.ShowDialog() == true)
            {
                // تحديث الكمية في النافذة الحالية
                var updatedProduct = ProductService.GetProductById(_product.Id);
                if (updatedProduct != null)
                {
                    TxtQuantity.Text = updatedProduct.Quantity.ToString();
                }
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(TxtProductName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المنتج", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtProductName.Focus();
                    return;
                }

                if (!decimal.TryParse(TxtPrice.Text, out decimal price) || price < 0)
                {
                    MessageBox.Show("يرجى إدخال سعر صحيح", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtPrice.Focus();
                    return;
                }

                if (!decimal.TryParse(TxtCost.Text, out decimal cost) || cost < 0)
                {
                    MessageBox.Show("يرجى إدخال تكلفة صحيحة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtCost.Focus();
                    return;
                }

                if (!int.TryParse(TxtQuantity.Text, out int quantity) || quantity < 0)
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtQuantity.Focus();
                    return;
                }

                // التحقق من عدم تكرار الباركود
                if (!string.IsNullOrWhiteSpace(TxtBarcode.Text))
                {
                    var existingProduct = ProductService.GetProductByBarcode(TxtBarcode.Text.Trim());
                    if (existingProduct != null && existingProduct.Id != _product.Id)
                    {
                        MessageBox.Show("هذا الباركود مستخدم بالفعل لمنتج آخر", "تنبيه", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        TxtBarcode.Focus();
                        return;
                    }
                }

                // تحديث بيانات المنتج
                _product.Name = TxtProductName.Text.Trim();
                _product.Category = CmbCategory.Text.Trim();
                _product.Price = price;
                _product.Cost = cost;
                _product.Quantity = quantity;
                _product.Barcode = TxtBarcode.Text.Trim();
                _product.IsActive = ChkIsActive.IsChecked ?? true;
                _product.Description = TxtDescription.Text.Trim();

                // حفظ التغييرات
                if (ProductService.UpdateProduct(_product))
                {
                    MessageBox.Show("تم حفظ التغييرات بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التغييرات", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        // تحديث الإحصائيات عند تغيير السعر أو التكلفة
        private void TxtPrice_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateStatistics();
        }

        private void TxtCost_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateStatistics();
        }
    }
}
