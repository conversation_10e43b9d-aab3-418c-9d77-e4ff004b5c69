using System;
using System.Windows;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class EndShiftWindow : Window
    {
        private readonly int _shiftId;

        public EndShiftWindow(int shiftId)
        {
            InitializeComponent();
            _shiftId = shiftId;
            LoadShiftInfo();
        }

        private void LoadShiftInfo()
        {
            try
            {
                var shift = ShiftService.GetShiftById(_shiftId);
                if (shift != null)
                {
                    TxtShiftInfo.Text = $"{shift.Name} - {shift.EmployeeName}";
                    
                    var duration = DateTime.Now - shift.StartTime;
                    TxtShiftDuration.Text = $"المدة: {duration.Hours:00}:{duration.Minutes:00}";
                    
                    var sales = ReportService.GetShiftSales(_shiftId);
                    TxtShiftSales.Text = $"إجمالي المبيعات: {sales:F2} ريال";
                    
                    // تعيين قيمة افتراضية للنقد المحصل
                    TxtTotalCash.Text = sales.ToString("F2");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل معلومات الشيفت: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEnd_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!decimal.TryParse(TxtTotalCash.Text, out decimal totalCash) || totalCash < 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ النقد المحصل بشكل صحيح", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtTotalCash.Focus();
                    return;
                }

                if (!decimal.TryParse(TxtTotalExpenses.Text, out decimal totalExpenses) || totalExpenses < 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ المصروفات بشكل صحيح", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtTotalExpenses.Focus();
                    return;
                }

                // تأكيد إنهاء الشيفت
                var result = MessageBox.Show("هل أنت متأكد من إنهاء الشيفت؟", "تأكيد", 
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    // إنهاء الشيفت
                    var success = ShiftService.EndShift(_shiftId, totalCash, totalExpenses, TxtNotes.Text.Trim());
                    
                    if (success)
                    {
                        MessageBox.Show("تم إنهاء الشيفت بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        DialogResult = true;
                        Close();
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنهاء الشيفت", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنهاء الشيفت: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
