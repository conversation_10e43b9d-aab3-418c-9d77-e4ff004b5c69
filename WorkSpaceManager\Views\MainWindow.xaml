<Window x:Class="WorkSpaceManager.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Work Space Manager - نظام إدارة المساحات المشتركة" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="Work Space Manager" 
                         FontSize="24" FontWeight="Bold" Foreground="White"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="TxtCurrentShift" Text="لا يوجد شيفت نشط" 
                             Foreground="White" Margin="0,0,20,0"/>
                    <Button x:Name="BtnThemeToggle" Content="🌙 الوضع الليلي" 
                          Background="Transparent" Foreground="White" BorderThickness="1"
                          Click="BtnThemeToggle_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Navigation and Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Navigation Menu -->
            <Border Grid.Column="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <StackPanel Margin="10">
                    <Button x:Name="BtnDashboard" Content="🏠 الرئيسية" 
                          Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
                          Click="BtnDashboard_Click"/>
                    <Button x:Name="BtnCustomers" Content="👥 العملاء" 
                          Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
                          Click="BtnCustomers_Click"/>
                    <Button x:Name="BtnSessions" Content="⏰ الجلسات" 
                          Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
                          Click="BtnSessions_Click"/>
                    <Button x:Name="BtnProducts" Content="🛍️ المنتجات" 
                          Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
                          Click="BtnProducts_Click"/>
                    <Button x:Name="BtnInvoices" Content="🧾 الفواتير" 
                          Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
                          Click="BtnInvoices_Click"/>
                    <Button x:Name="BtnShifts" Content="🔄 الشيفتات" 
                          Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
                          Click="BtnShifts_Click"/>
                    <Button x:Name="BtnReports" Content="📊 التقارير" 
                          Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
                          Click="BtnReports_Click"/>
                    <Button x:Name="BtnSettings" Content="⚙️ الإعدادات" 
                          Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
                          Click="BtnSettings_Click"/>
                </StackPanel>
            </Border>

            <!-- Main Content -->
            <Frame x:Name="MainFrame" Grid.Column="1" NavigationUIVisibility="Hidden"/>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#E0E0E0" Padding="10">
            <TextBlock x:Name="TxtShiftTime" Text="مرحباً بك في Work Space Manager"/>
        </Border>
    </Grid>
</Window>
