using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class ProductsPage : Page
    {
        private List<Product> _allProducts;
        private Product _selectedProduct;

        public ProductsPage()
        {
            InitializeComponent();
            LoadProducts();
            LoadCategories();
        }

        private void LoadProducts()
        {
            try
            {
                _allProducts = ProductService.GetAllProducts();
                DgProducts.ItemsSource = _allProducts;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة المنتجات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCategories()
        {
            try
            {
                var categories = ProductService.GetCategories();
                categories.Insert(0, "جميع الفئات");
                CmbCategoryFilter.ItemsSource = categories;
                CmbCategoryFilter.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفئات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterProducts();
        }

        private void CmbCategoryFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterProducts();
        }

        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            FilterProducts();
        }

        private void FilterProducts()
        {
            if (_allProducts == null) return;

            var searchTerm = TxtSearch.Text.Trim().ToLower();
            var selectedCategory = CmbCategoryFilter.SelectedItem as string;
            
            var filteredProducts = _allProducts.AsEnumerable();

            // فلتر النص
            if (!string.IsNullOrEmpty(searchTerm))
            {
                filteredProducts = filteredProducts.Where(p =>
                    p.Name.ToLower().Contains(searchTerm) ||
                    (p.Category != null && p.Category.ToLower().Contains(searchTerm)) ||
                    (p.Barcode != null && p.Barcode.Contains(searchTerm))
                );
            }

            // فلتر الفئة
            if (!string.IsNullOrEmpty(selectedCategory) && selectedCategory != "جميع الفئات")
            {
                filteredProducts = filteredProducts.Where(p => p.Category == selectedCategory);
            }

            DgProducts.ItemsSource = filteredProducts.ToList();
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadProducts();
            LoadCategories();
            TxtSearch.Text = "";
            CmbCategoryFilter.SelectedIndex = 0;
        }

        private void BtnAddProduct_Click(object sender, RoutedEventArgs e)
        {
            var addProductWindow = new AddProductWindow();
            if (addProductWindow.ShowDialog() == true)
            {
                LoadProducts();
                LoadCategories();
            }
        }

        private void BtnEditProduct_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار منتج للتعديل", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var editProductWindow = new EditProductWindow(_selectedProduct);
            if (editProductWindow.ShowDialog() == true)
            {
                LoadProducts();
                LoadCategories();
            }
        }

        private void BtnDeleteProduct_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null)
            {
                MessageBox.Show("يرجى اختيار منتج للحذف", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show(
                $"هل أنت متأكد من حذف المنتج '{_selectedProduct.Name}'؟\nهذا الإجراء لا يمكن التراجع عنه.", 
                "تأكيد الحذف", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    if (ProductService.DeleteProduct(_selectedProduct.Id))
                    {
                        MessageBox.Show("تم حذف المنتج بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadProducts();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف المنتج", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void DgProducts_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedProduct = DgProducts.SelectedItem as Product;
            
            BtnEditProduct.IsEnabled = _selectedProduct != null;
            BtnDeleteProduct.IsEnabled = _selectedProduct != null;
        }

        private void DgProducts_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (_selectedProduct != null)
            {
                BtnEditProduct_Click(sender, e);
            }
        }

        private void BtnUpdateQuantity_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.Tag is int productId)
            {
                var product = _allProducts.FirstOrDefault(p => p.Id == productId);
                if (product != null)
                {
                    var updateQuantityWindow = new UpdateQuantityWindow(product);
                    if (updateQuantityWindow.ShowDialog() == true)
                    {
                        LoadProducts();
                    }
                }
            }
        }

        private void BtnAddToSession_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.Tag is int productId)
            {
                var product = _allProducts.FirstOrDefault(p => p.Id == productId);
                if (product != null)
                {
                    var addToSessionWindow = new AddProductToSessionWindow(product);
                    addToSessionWindow.ShowDialog();
                }
            }
        }
    }
}
