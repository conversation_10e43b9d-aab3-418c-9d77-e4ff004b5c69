using System;
using System.Linq;
using System.Windows;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class StartSessionWindow : Window
    {
        public StartSessionWindow()
        {
            InitializeComponent();
            LoadCustomers();
        }

        private void LoadCustomers()
        {
            try
            {
                var customers = CustomerService.GetAllCustomers();
                CmbCustomers.ItemsSource = customers;
                
                if (customers.Any())
                {
                    CmbCustomers.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة العملاء: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddNewCustomer_Click(object sender, RoutedEventArgs e)
        {
            var addCustomerWindow = new AddCustomerWindow();
            if (addCustomerWindow.ShowDialog() == true)
            {
                LoadCustomers();
                // تحديد العميل الجديد
                var newCustomer = CustomerService.GetAllCustomers().LastOrDefault();
                if (newCustomer != null)
                {
                    CmbCustomers.SelectedValue = newCustomer.Id;
                }
            }
        }

        private void BtnStart_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (CmbCustomers.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار العميل", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(TxtHourlyRate.Text, out decimal hourlyRate) || hourlyRate <= 0)
                {
                    MessageBox.Show("يرجى إدخال سعر ساعة صحيح", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var customerId = (int)CmbCustomers.SelectedValue;
                
                // التحقق من وجود شيفت نشط
                var currentShift = ShiftService.GetCurrentShift();
                if (currentShift == null)
                {
                    var result = MessageBox.Show("لا يوجد شيفت نشط. هل تريد بدء شيفت جديد؟", "تأكيد", 
                        MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        var startShiftWindow = new StartShiftWindow();
                        if (startShiftWindow.ShowDialog() != true)
                        {
                            return;
                        }
                        currentShift = ShiftService.GetCurrentShift();
                    }
                    else
                    {
                        return;
                    }
                }

                // بدء الجلسة
                var sessionId = SessionService.StartSession(customerId, hourlyRate, currentShift.Id);
                
                if (sessionId > 0)
                {
                    MessageBox.Show("تم بدء الجلسة بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في بدء الجلسة", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء الجلسة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
