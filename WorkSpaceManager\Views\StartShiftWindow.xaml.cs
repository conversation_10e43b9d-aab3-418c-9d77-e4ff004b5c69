using System;
using System.Windows;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class StartShiftWindow : Window
    {
        public StartShiftWindow()
        {
            InitializeComponent();
            
            // تعيين قيم افتراضية
            TxtShiftName.Text = $"شيفت {DateTime.Now:yyyy-MM-dd HH:mm}";
        }

        private void BtnStart_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(TxtShiftName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الشيفت", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtShiftName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(TxtEmployeeName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الموظف", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtEmployeeName.Focus();
                    return;
                }

                // التحقق من عدم وجود شيفت نشط
                var currentShift = ShiftService.GetCurrentShift();
                if (currentShift != null)
                {
                    var result = MessageBox.Show(
                        $"يوجد شيفت نشط حالياً: {currentShift.Name}\nهل تريد إنهاؤه وبدء شيفت جديد؟", 
                        "تأكيد", 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        var endShiftWindow = new EndShiftWindow(currentShift.Id);
                        if (endShiftWindow.ShowDialog() != true)
                        {
                            return;
                        }
                    }
                    else
                    {
                        return;
                    }
                }

                // بدء الشيفت الجديد
                var shiftId = ShiftService.StartShift(
                    TxtShiftName.Text.Trim(),
                    TxtEmployeeName.Text.Trim(),
                    TxtNotes.Text.Trim());
                
                if (shiftId > 0)
                {
                    MessageBox.Show("تم بدء الشيفت بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في بدء الشيفت", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في بدء الشيفت: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
