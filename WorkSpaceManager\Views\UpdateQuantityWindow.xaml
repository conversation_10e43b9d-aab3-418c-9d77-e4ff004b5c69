<Window x:Class="WorkSpaceManager.Views.UpdateQuantityWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تحديث كمية المنتج" 
        Height="400" Width="450"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" 
                   Text="تحديث كمية المنتج" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Margin="0,0,0,20"
                   Foreground="#2196F3"
                   HorizontalAlignment="Center"/>

        <!-- النموذج -->
        <StackPanel Grid.Row="1" Margin="0,0,0,20">
            
            <!-- معلومات المنتج -->
            <Border Background="#F5F5F5" 
                    CornerRadius="5" 
                    Padding="15" 
                    Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="معلومات المنتج" 
                             FontWeight="Bold" 
                             FontSize="16" 
                             Margin="0,0,0,10"/>
                    
                    <TextBlock x:Name="TxtProductName" 
                             Text="اسم المنتج" 
                             FontSize="14" 
                             FontWeight="Bold"/>
                    
                    <TextBlock x:Name="TxtProductCategory" 
                             Text="الفئة" 
                             FontSize="12" 
                             Foreground="Gray"/>
                </StackPanel>
            </Border>

            <!-- الكمية الحالية -->
            <TextBlock Text="الكمية الحالية:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtCurrentQuantity" 
                     IsReadOnly="True"
                     Background="#F5F5F5"
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"
                     FontSize="16"
                     FontWeight="Bold"/>

            <!-- نوع التحديث -->
            <TextBlock Text="نوع التحديث:" FontWeight="Bold" Margin="0,0,0,5"/>
            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                <RadioButton x:Name="RbAdd" 
                           Content="إضافة كمية" 
                           IsChecked="True"
                           Margin="0,0,20,0"
                           Checked="UpdateType_Changed"/>
                
                <RadioButton x:Name="RbSubtract" 
                           Content="خصم كمية" 
                           Margin="0,0,20,0"
                           Checked="UpdateType_Changed"/>
                
                <RadioButton x:Name="RbSet" 
                           Content="تعيين كمية جديدة" 
                           Checked="UpdateType_Changed"/>
            </StackPanel>

            <!-- الكمية -->
            <TextBlock x:Name="LblQuantity" Text="الكمية المراد إضافتها:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtQuantity" 
                     Text="0"
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"
                     FontSize="16"
                     TextChanged="TxtQuantity_TextChanged"/>

            <!-- الكمية الجديدة (معاينة) -->
            <TextBlock Text="الكمية الجديدة (معاينة):" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtNewQuantity" 
                     IsReadOnly="True"
                     Background="#E8F5E8"
                     Margin="0,0,0,15"
                     Height="35"
                     VerticalContentAlignment="Center"
                     FontSize="16"
                     FontWeight="Bold"
                     Foreground="#4CAF50"/>

            <!-- ملاحظات -->
            <TextBlock Text="ملاحظات (اختياري):" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtNotes" 
                     Height="60"
                     TextWrapping="Wrap"
                     AcceptsReturn="True"
                     VerticalScrollBarVisibility="Auto"/>

        </StackPanel>

        <!-- الأزرار -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            
            <Button x:Name="BtnUpdate" 
                    Content="📦 تحديث الكمية" 
                    Background="#4CAF50" 
                    Foreground="White" 
                    Padding="20,10" 
                    Margin="0,0,10,0"
                    Click="BtnUpdate_Click"/>
            
            <Button x:Name="BtnCancel" 
                    Content="إلغاء" 
                    Background="#F44336" 
                    Foreground="White" 
                    Padding="20,10"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
