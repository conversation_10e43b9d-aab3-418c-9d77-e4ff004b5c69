using System;
using System.Windows;
using WorkSpaceManager.Models;
using WorkSpaceManager.Services;

namespace WorkSpaceManager.Views
{
    public partial class UpdateQuantityWindow : Window
    {
        private readonly Product _product;
        private int _currentQuantity;

        public UpdateQuantityWindow(Product product)
        {
            InitializeComponent();
            _product = product;
            LoadProductData();
        }

        private void LoadProductData()
        {
            if (_product != null)
            {
                TxtProductName.Text = _product.Name;
                TxtProductCategory.Text = string.IsNullOrEmpty(_product.Category) ? "غير محدد" : _product.Category;
                _currentQuantity = _product.Quantity;
                TxtCurrentQuantity.Text = _currentQuantity.ToString();
                
                UpdatePreview();
            }
        }

        private void UpdateType_Changed(object sender, RoutedEventArgs e)
        {
            if (RbAdd.IsChecked == true)
            {
                LblQuantity.Text = "الكمية المراد إضافتها:";
            }
            else if (RbSubtract.IsChecked == true)
            {
                LblQuantity.Text = "الكمية المراد خصمها:";
            }
            else if (RbSet.IsChecked == true)
            {
                LblQuantity.Text = "الكمية الجديدة:";
            }
            
            UpdatePreview();
        }

        private void TxtQuantity_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            if (!int.TryParse(TxtQuantity.Text, out int quantity))
            {
                TxtNewQuantity.Text = "كمية غير صحيحة";
                TxtNewQuantity.Foreground = System.Windows.Media.Brushes.Red;
                return;
            }

            int newQuantity = _currentQuantity;

            if (RbAdd.IsChecked == true)
            {
                newQuantity = _currentQuantity + quantity;
            }
            else if (RbSubtract.IsChecked == true)
            {
                newQuantity = _currentQuantity - quantity;
            }
            else if (RbSet.IsChecked == true)
            {
                newQuantity = quantity;
            }

            TxtNewQuantity.Text = newQuantity.ToString();
            
            if (newQuantity < 0)
            {
                TxtNewQuantity.Foreground = System.Windows.Media.Brushes.Red;
            }
            else
            {
                TxtNewQuantity.Foreground = System.Windows.Media.Brushes.Green;
            }
        }

        private void BtnUpdate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!int.TryParse(TxtQuantity.Text, out int quantity))
                {
                    MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtQuantity.Focus();
                    return;
                }

                if (quantity < 0)
                {
                    MessageBox.Show("لا يمكن أن تكون الكمية سالبة", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtQuantity.Focus();
                    return;
                }

                // حساب الكمية الجديدة
                int newQuantity = _currentQuantity;

                if (RbAdd.IsChecked == true)
                {
                    newQuantity = _currentQuantity + quantity;
                }
                else if (RbSubtract.IsChecked == true)
                {
                    newQuantity = _currentQuantity - quantity;
                    
                    if (newQuantity < 0)
                    {
                        var result = MessageBox.Show(
                            "الكمية الجديدة ستكون سالبة. هل تريد المتابعة؟", 
                            "تأكيد", 
                            MessageBoxButton.YesNo, 
                            MessageBoxImage.Question);
                        
                        if (result == MessageBoxResult.No)
                        {
                            return;
                        }
                    }
                }
                else if (RbSet.IsChecked == true)
                {
                    newQuantity = quantity;
                }

                // تحديث الكمية في قاعدة البيانات
                if (ProductService.UpdateProductQuantity(_product.Id, newQuantity))
                {
                    // تسجيل العملية (يمكن إضافة جدول لتتبع تغييرات المخزون)
                    var operationType = RbAdd.IsChecked == true ? "إضافة" : 
                                       RbSubtract.IsChecked == true ? "خصم" : "تعيين";
                    
                    var message = $"تم تحديث كمية المنتج بنجاح\n" +
                                 $"العملية: {operationType}\n" +
                                 $"الكمية السابقة: {_currentQuantity}\n" +
                                 $"الكمية الجديدة: {newQuantity}";

                    MessageBox.Show(message, "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في تحديث كمية المنتج", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الكمية: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
