@echo off
echo ===============================================
echo    Work Space Manager - إصلاح تلقائي
echo ===============================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed
    pause
    exit /b 1
)

echo .NET SDK found. Attempting auto-fix...
echo.

REM Clean previous builds
echo Cleaning previous builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

REM Try building with Material Design first
echo ===============================================
echo Attempt 1: Building with Material Design...
echo ===============================================
dotnet restore WorkSpaceManager.csproj
dotnet build WorkSpaceManager.csproj --configuration Release --verbosity minimal

if %errorlevel% equ 0 (
    echo.
    echo ===============================================
    echo SUCCESS: Build completed with Material Design!
    echo ===============================================
    goto :success
)

echo.
echo ===============================================
echo Attempt 1 failed. Trying without Material Design...
echo ===============================================

REM Backup original files
copy "App.xaml" "App-Original.xaml.bak" >nul 2>&1
copy "Views\MainWindow.xaml" "Views\MainWindow-Original.xaml.bak" >nul 2>&1

REM Use simple versions
copy "App-Simple.xaml" "App.xaml" /Y

REM Create simple project file without Material Design
echo Creating simple project file...
(
    echo ^<Project Sdk="Microsoft.NET.Sdk"^>
    echo   ^<PropertyGroup^>
    echo     ^<OutputType^>WinExe^</OutputType^>
    echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^>
    echo     ^<UseWPF^>true^</UseWPF^>
    echo     ^<RootNamespace^>WorkSpaceManager^</RootNamespace^>
    echo     ^<AssemblyName^>WorkSpaceManager^</AssemblyName^>
    echo     ^<AssemblyTitle^>Work Space Manager^</AssemblyTitle^>
    echo   ^</PropertyGroup^>
    echo   ^<ItemGroup^>
    echo     ^<PackageReference Include="Microsoft.Data.Sqlite" Version="6.0.0" /^>
    echo     ^<PackageReference Include="System.Drawing.Common" Version="6.0.0" /^>
    echo   ^</ItemGroup^>
    echo ^</Project^>
) > WorkSpaceManagerSimple.csproj

REM Clean and try again
dotnet clean WorkSpaceManagerSimple.csproj
dotnet restore WorkSpaceManagerSimple.csproj
dotnet build WorkSpaceManagerSimple.csproj --configuration Release --verbosity minimal

if %errorlevel% equ 0 (
    echo.
    echo ===============================================
    echo SUCCESS: Build completed without Material Design!
    echo ===============================================
    echo.
    echo Note: Using simplified UI without Material Design
    echo You can run the application with:
    echo   dotnet run --project WorkSpaceManagerSimple.csproj
    echo.
    goto :success
)

echo.
echo ===============================================
echo Both attempts failed. Manual intervention needed.
echo ===============================================
echo.
echo Please check:
echo 1. All XAML files for syntax errors
echo 2. Missing using statements in C# files
echo 3. Package compatibility issues
echo.
goto :error

:success
echo.
echo You can now run the application!
echo.
pause
exit /b 0

:error
echo.
echo Build failed. Check the errors above.
echo.
pause
exit /b 1
