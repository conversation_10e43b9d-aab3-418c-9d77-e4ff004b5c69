@echo off
echo ===============================================
echo    Work Space Manager - بناء نظيف
echo ===============================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed or not in PATH
    echo Please install .NET 6.0 SDK from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET SDK found. Building project...
echo.

REM Remove any extra project files to avoid conflicts
echo Cleaning up project files...
if exist "WorkSpaceManager-Simple.csproj" del "WorkSpaceManager-Simple.csproj"

REM Clean previous builds
echo Cleaning previous builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

REM Clear NuGet cache
echo Clearing NuGet cache...
dotnet nuget locals all --clear

REM Restore packages for specific project
echo Restoring NuGet packages...
dotnet restore WorkSpaceManager.csproj
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages
    echo Trying alternative approach...
    
    REM Try with simpler project file
    echo Creating minimal project file...
    (
        echo ^<Project Sdk="Microsoft.NET.Sdk"^>
        echo   ^<PropertyGroup^>
        echo     ^<OutputType^>WinExe^</OutputType^>
        echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^>
        echo     ^<UseWPF^>true^</UseWPF^>
        echo   ^</PropertyGroup^>
        echo   ^<ItemGroup^>
        echo     ^<PackageReference Include="Microsoft.Data.Sqlite" Version="6.0.0" /^>
        echo     ^<PackageReference Include="System.Drawing.Common" Version="6.0.0" /^>
        echo   ^</ItemGroup^>
        echo ^</Project^>
    ) > WorkSpaceManager-Minimal.csproj
    
    dotnet restore WorkSpaceManager-Minimal.csproj
    if %errorlevel% neq 0 (
        echo ERROR: Still failed to restore packages
        pause
        exit /b 1
    )
    
    REM Use minimal project for build
    copy "WorkSpaceManager-Minimal.csproj" "WorkSpaceManager.csproj" /Y
)

REM Build the project
echo Building the project...
dotnet build WorkSpaceManager.csproj --configuration Release --verbosity minimal
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    echo.
    echo Trying to build with more details...
    dotnet build WorkSpaceManager.csproj --configuration Release --verbosity normal
    pause
    exit /b 1
)

REM Test run to make sure it works
echo Testing the build...
dotnet run --project WorkSpaceManager.csproj --configuration Release --no-build -- --test
if %errorlevel% neq 0 (
    echo Warning: Test run failed, but build succeeded
)

echo.
echo ===============================================
echo Build completed successfully!
echo.
echo You can now run the application with:
echo   dotnet run --project WorkSpaceManager.csproj
echo.
echo Or build for distribution:
echo   dotnet publish WorkSpaceManager.csproj -c Release -o publish
echo ===============================================
echo.
pause
