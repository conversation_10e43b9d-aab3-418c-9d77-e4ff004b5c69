@echo off
echo ===============================================
echo    Work Space Manager - English Build
echo ===============================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed
    pause
    exit /b 1
)

echo .NET SDK found. Starting build process...
echo.

REM Step 1: Clean everything
echo Step 1: Complete cleanup...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

REM Remove problematic files
if exist "WorkSpaceManager-NoMaterial.csproj" del "WorkSpaceManager-NoMaterial.csproj"
if exist "Resources\Styles\CardStyles.xaml" del "Resources\Styles\CardStyles.xaml"
if exist "Resources\Styles\ButtonStyles.xaml" del "Resources\Styles\ButtonStyles.xaml"
if exist "Resources\Styles\TextStyles.xaml" del "Resources\Styles\TextStyles.xaml"

REM Step 2: Create clean project file
echo Step 2: Creating clean project file...
(
    echo ^<Project Sdk="Microsoft.NET.Sdk"^>
    echo   ^<PropertyGroup^>
    echo     ^<OutputType^>WinExe^</OutputType^>
    echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^>
    echo     ^<UseWPF^>true^</UseWPF^>
    echo     ^<RootNamespace^>WorkSpaceManager^</RootNamespace^>
    echo     ^<AssemblyName^>WorkSpaceManager^</AssemblyName^>
    echo     ^<AssemblyTitle^>Work Space Manager^</AssemblyTitle^>
    echo   ^</PropertyGroup^>
    echo   ^<ItemGroup^>
    echo     ^<PackageReference Include="Microsoft.Data.Sqlite" Version="6.0.0" /^>
    echo     ^<PackageReference Include="System.Drawing.Common" Version="6.0.0" /^>
    echo   ^</ItemGroup^>
    echo ^</Project^>
) > WorkSpaceManagerClean.csproj

REM Step 3: Create simple App.xaml
echo Step 3: Creating simple App.xaml...
(
    echo ^<Application x:Class="WorkSpaceManager.App"
    echo              xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    echo              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    echo              StartupUri="Views/MainWindow.xaml"^>
    echo     ^<Application.Resources^>
    echo     ^</Application.Resources^>
    echo ^</Application^>
) > App.xaml

REM Step 4: Create simple MainWindow.xaml
echo Step 4: Creating simple MainWindow.xaml...
(
    echo ^<Window x:Class="WorkSpaceManager.Views.MainWindow"
    echo         xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    echo         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    echo         Title="Work Space Manager"
    echo         Height="600" Width="800"
    echo         WindowStartupLocation="CenterScreen"^>
    echo     ^<Grid^>
    echo         ^<TextBlock Text="Work Space Manager"
    echo                  FontSize="24" FontWeight="Bold"
    echo                  HorizontalAlignment="Center"
    echo                  VerticalAlignment="Center"/^>
    echo     ^</Grid^>
    echo ^</Window^>
) > Views\MainWindow.xaml

REM Copy simple code-behind files
if exist "Views\MainWindow-Simple.xaml.cs" (
    copy "Views\MainWindow-Simple.xaml.cs" "Views\MainWindow.xaml.cs" /Y
)
if exist "App-Simple.xaml.cs" (
    copy "App-Simple.xaml.cs" "App.xaml.cs" /Y
)

REM Step 5: Build the project
echo Step 5: Building the project...
dotnet clean WorkSpaceManagerClean.csproj
dotnet restore WorkSpaceManagerClean.csproj
dotnet build WorkSpaceManagerClean.csproj --configuration Release --verbosity minimal

if %errorlevel% equ 0 (
    echo.
    echo ===============================================
    echo SUCCESS: Build completed successfully!
    echo ===============================================
    echo.
    echo You can now run the application:
    echo   dotnet run --project WorkSpaceManagerClean.csproj
    echo.
    echo Or create executable:
    echo   dotnet publish WorkSpaceManagerClean.csproj -c Release -o publish
    echo.
    goto :success
) else (
    echo.
    echo Build failed. Trying minimal approach...
    
    REM Create absolute minimal project
    (
        echo ^<Project Sdk="Microsoft.NET.Sdk"^>
        echo   ^<PropertyGroup^>
        echo     ^<OutputType^>WinExe^</OutputType^>
        echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^>
        echo     ^<UseWPF^>true^</UseWPF^>
        echo   ^</PropertyGroup^>
        echo ^</Project^>
    ) > WorkSpaceManagerMinimal.csproj
    
    dotnet clean WorkSpaceManagerMinimal.csproj
    dotnet restore WorkSpaceManagerMinimal.csproj
    dotnet build WorkSpaceManagerMinimal.csproj --configuration Release
    
    if %errorlevel% equ 0 (
        echo.
        echo ===============================================
        echo SUCCESS: Minimal build completed!
        echo ===============================================
        echo.
        echo Run with: dotnet run --project WorkSpaceManagerMinimal.csproj
        goto :success
    ) else (
        goto :error
    )
)

:success
echo.
echo Build completed successfully!
echo.
pause
exit /b 0

:error
echo.
echo ===============================================
echo Build failed completely.
echo ===============================================
echo.
echo Please check:
echo 1. .NET 6.0 SDK is properly installed
echo 2. No file permission issues
echo 3. No antivirus blocking
echo.
pause
exit /b 1
