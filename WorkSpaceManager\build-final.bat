@echo off
echo ===============================================
echo    Work Space Manager - البناء النهائي
echo ===============================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed
    pause
    exit /b 1
)

echo .NET SDK found. Building project...
echo.

REM Clean everything
echo Cleaning all build artifacts...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

REM Remove problematic files
if exist "WorkSpaceManager-NoMaterial.csproj" del "WorkSpaceManager-NoMaterial.csproj"

REM Use the simple App.xaml
if exist "App-Simple.xaml" (
    echo Using simplified App.xaml...
    copy "App-Simple.xaml" "App.xaml" /Y
)

REM Build with the simple project file
echo Building with simplified project...
dotnet clean WorkSpaceManagerSimple.csproj
dotnet restore WorkSpaceManagerSimple.csproj
dotnet build WorkSpaceManagerSimple.csproj --configuration Release

if %errorlevel% equ 0 (
    echo.
    echo ===============================================
    echo SUCCESS: Build completed successfully!
    echo ===============================================
    echo.
    echo You can now run the application with:
    echo   dotnet run --project WorkSpaceManagerSimple.csproj
    echo.
    echo Or create a published version:
    echo   dotnet publish WorkSpaceManagerSimple.csproj -c Release -o publish
    echo.
    goto :success
) else (
    echo.
    echo ===============================================
    echo Build failed. Trying alternative approach...
    echo ===============================================
    
    REM Try copying the simple project over the main one
    copy "WorkSpaceManagerSimple.csproj" "WorkSpaceManager.csproj" /Y
    
    dotnet clean WorkSpaceManager.csproj
    dotnet restore WorkSpaceManager.csproj
    dotnet build WorkSpaceManager.csproj --configuration Release
    
    if %errorlevel% equ 0 (
        echo.
        echo ===============================================
        echo SUCCESS: Build completed with main project file!
        echo ===============================================
        echo.
        echo You can now run the application with:
        echo   dotnet run --project WorkSpaceManager.csproj
        echo.
        goto :success
    ) else (
        goto :error
    )
)

:success
echo.
echo Build completed successfully!
echo The application is ready to run.
echo.
pause
exit /b 0

:error
echo.
echo ===============================================
echo Build failed completely.
echo ===============================================
echo.
echo Please check:
echo 1. All C# files compile correctly
echo 2. All XAML files have correct syntax
echo 3. All required packages are available
echo.
echo Try running: dotnet build --verbosity detailed
echo for more information about the errors.
echo.
pause
exit /b 1
