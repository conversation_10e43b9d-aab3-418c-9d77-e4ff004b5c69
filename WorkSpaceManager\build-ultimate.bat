@echo off
echo ===============================================
echo    Work Space Manager - البناء النهائي المطلق
echo ===============================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed
    pause
    exit /b 1
)

echo .NET SDK found. Starting ultimate build process...
echo.

REM Step 1: Clean everything
echo الخطوة 1: تنظيف شامل...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"

REM Remove problematic project files
if exist "WorkSpaceManager-NoMaterial.csproj" del "WorkSpaceManager-NoMaterial.csproj"

REM Step 2: Fix XAML files
echo الخطوة 2: إصلاح ملفات XAML...
call fix-xaml-files.bat

REM Step 3: Use simple App.xaml
echo الخطوة 3: استخدام App.xaml مبسط...
if exist "App-Simple.xaml" (
    copy "App-Simple.xaml" "App.xaml" /Y
)

REM Step 4: Build with simple project
echo الخطوة 4: البناء بالمشروع المبسط...
dotnet clean WorkSpaceManagerSimple.csproj
dotnet restore WorkSpaceManagerSimple.csproj
dotnet build WorkSpaceManagerSimple.csproj --configuration Release --verbosity minimal

if %errorlevel% equ 0 (
    echo.
    echo ===============================================
    echo 🎉 SUCCESS: البناء مكتمل بنجاح!
    echo ===============================================
    echo.
    echo يمكنك الآن تشغيل البرنامج:
    echo   dotnet run --project WorkSpaceManagerSimple.csproj
    echo.
    echo أو إنشاء ملف تنفيذي:
    echo   dotnet publish WorkSpaceManagerSimple.csproj -c Release -o publish
    echo.
    
    REM Test the build
    echo اختبار سريع للبناء...
    dotnet build WorkSpaceManagerSimple.csproj --configuration Release --verbosity quiet
    if %errorlevel% equ 0 (
        echo ✅ البناء مستقر ويعمل بشكل صحيح
    )
    
    goto :success
) else (
    echo.
    echo ❌ فشل البناء. محاولة حل بديل...
    
    REM Alternative: Create minimal project
    echo إنشاء مشروع أدنى...
    (
        echo ^<Project Sdk="Microsoft.NET.Sdk"^>
        echo   ^<PropertyGroup^>
        echo     ^<OutputType^>WinExe^</OutputType^>
        echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^>
        echo     ^<UseWPF^>true^</UseWPF^>
        echo     ^<RootNamespace^>WorkSpaceManager^</RootNamespace^>
        echo     ^<AssemblyName^>WorkSpaceManager^</AssemblyName^>
        echo   ^</PropertyGroup^>
        echo   ^<ItemGroup^>
        echo     ^<PackageReference Include="Microsoft.Data.Sqlite" Version="6.0.0" /^>
        echo   ^</ItemGroup^>
        echo ^</Project^>
    ) > WorkSpaceManagerMinimal.csproj
    
    dotnet clean WorkSpaceManagerMinimal.csproj
    dotnet restore WorkSpaceManagerMinimal.csproj
    dotnet build WorkSpaceManagerMinimal.csproj --configuration Release
    
    if %errorlevel% equ 0 (
        echo.
        echo ===============================================
        echo 🎉 SUCCESS: البناء مكتمل بالمشروع الأدنى!
        echo ===============================================
        echo.
        echo يمكنك تشغيل البرنامج:
        echo   dotnet run --project WorkSpaceManagerMinimal.csproj
        echo.
        goto :success
    ) else (
        goto :error
    )
)

:success
echo.
echo 🎊 تهانينا! تم بناء البرنامج بنجاح
echo.
echo الملفات الجاهزة:
if exist "bin\Release\net6.0-windows\WorkSpaceManager.exe" (
    echo ✅ ملف تنفيذي: bin\Release\net6.0-windows\WorkSpaceManager.exe
)
echo ✅ مشروع جاهز للتشغيل
echo.
echo للتشغيل الفوري:
echo   dotnet run --project WorkSpaceManagerSimple.csproj
echo.
echo لإنشاء ملف توزيع:
echo   dotnet publish WorkSpaceManagerSimple.csproj -c Release -o publish --self-contained
echo.
pause
exit /b 0

:error
echo.
echo ===============================================
echo ❌ فشل البناء نهائياً
echo ===============================================
echo.
echo يرجى التحقق من:
echo 1. تثبيت .NET 6.0 SDK بشكل صحيح
echo 2. عدم وجود ملفات تالفة في المشروع
echo 3. صلاحيات الكتابة في المجلد
echo.
echo للمساعدة، شغل:
echo   dotnet build WorkSpaceManagerSimple.csproj --verbosity detailed
echo.
pause
exit /b 1
