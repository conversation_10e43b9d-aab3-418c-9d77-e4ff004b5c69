@echo off
echo ===============================================
echo    Work Space Manager - Final Clean Build
echo ===============================================
echo.

REM Check if .NET SDK is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET SDK is not installed
    pause
    exit /b 1
)

echo .NET SDK found. Starting final clean build...
echo.

REM Step 1: Remove ALL project files
echo Step 1: Removing ALL project files...
del *.csproj 2>nul
del *.sln 2>nul

REM Step 2: Complete cleanup
echo Step 2: Complete cleanup...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "publish" rmdir /s /q "publish"
if exist "Backup" rmdir /s /q "Backup"
if exist "Resources" rmdir /s /q "Resources"

REM Step 3: Remove problematic XAML files
echo Step 3: Removing problematic XAML files...
if exist "Views" (
    del "Views\*.xaml" 2>nul
)

REM Step 4: Create the ONLY project file
echo Step 4: Creating single project file...
(
    echo ^<Project Sdk="Microsoft.NET.Sdk"^>
    echo   ^<PropertyGroup^>
    echo     ^<OutputType^>WinExe^</OutputType^>
    echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^>
    echo     ^<UseWPF^>true^</UseWPF^>
    echo     ^<RootNamespace^>WorkSpaceManager^</RootNamespace^>
    echo     ^<AssemblyName^>WorkSpaceManager^</AssemblyName^>
    echo   ^</PropertyGroup^>
    echo   ^<ItemGroup^>
    echo     ^<PackageReference Include="Microsoft.Data.Sqlite" Version="6.0.0" /^>
    echo   ^</ItemGroup^>
    echo ^</Project^>
) > WorkSpaceManager.csproj

echo Created WorkSpaceManager.csproj as the ONLY project file.

REM Step 5: Create minimal App.xaml
echo Step 5: Creating minimal App.xaml...
(
    echo ^<Application x:Class="WorkSpaceManager.App"
    echo              xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    echo              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    echo              StartupUri="Views/MainWindow.xaml"^>
    echo ^</Application^>
) > App.xaml

REM Step 6: Create minimal MainWindow.xaml
echo Step 6: Creating minimal MainWindow.xaml...
if not exist "Views" mkdir "Views"
(
    echo ^<Window x:Class="WorkSpaceManager.Views.MainWindow"
    echo         xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    echo         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    echo         Title="Work Space Manager" 
    echo         Height="600" Width="800"
    echo         WindowStartupLocation="CenterScreen"^>
    echo     ^<Grid^>
    echo         ^<StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"^>
    echo             ^<TextBlock Text="Work Space Manager" 
    echo                      FontSize="32" FontWeight="Bold" 
    echo                      HorizontalAlignment="Center" Margin="0,0,0,20"/^>
    echo             ^<TextBlock Text="Clean build successful!" 
    echo                      FontSize="16" 
    echo                      HorizontalAlignment="Center"/^>
    echo             ^<Button Content="Test Button" 
    echo                   Width="150" Height="40" 
    echo                   Margin="0,20,0,0"/^>
    echo         ^</StackPanel^>
    echo     ^</Grid^>
    echo ^</Window^>
) > Views\MainWindow.xaml

REM Step 7: Create code-behind files
echo Step 7: Creating code-behind files...
(
    echo using System.Windows;
    echo.
    echo namespace WorkSpaceManager
    echo {
    echo     public partial class App : Application
    echo     {
    echo     }
    echo }
) > App.xaml.cs

(
    echo using System.Windows;
    echo.
    echo namespace WorkSpaceManager.Views
    echo {
    echo     public partial class MainWindow : Window
    echo     {
    echo         public MainWindow()
    echo         {
    echo             InitializeComponent();
    echo         }
    echo     }
    echo }
) > Views\MainWindow.xaml.cs

REM Step 8: Verify only one project file exists
echo Step 8: Verifying project files...
set count=0
for %%f in (*.csproj) do set /a count+=1
echo Found %count% project file(s).

if %count% neq 1 (
    echo ERROR: Expected exactly 1 project file, found %count%
    goto :error
)

echo Confirmed: Only WorkSpaceManager.csproj exists.

REM Step 9: Build the project
echo Step 9: Building the project...
dotnet clean WorkSpaceManager.csproj
dotnet restore WorkSpaceManager.csproj
dotnet build WorkSpaceManager.csproj --configuration Release

if %errorlevel% equ 0 (
    echo.
    echo ===============================================
    echo SUCCESS: Final clean build completed!
    echo ===============================================
    echo.
    echo Project file: WorkSpaceManager.csproj
    echo.
    echo Run the application:
    echo   dotnet run --project WorkSpaceManager.csproj
    echo.
    echo Or use the simple command:
    echo   dotnet run
    echo.
    goto :success
) else (
    echo.
    echo Build failed. Trying absolute minimal...
    
    REM Create absolute minimal project
    (
        echo ^<Project Sdk="Microsoft.NET.Sdk"^>
        echo   ^<PropertyGroup^>
        echo     ^<OutputType^>WinExe^</OutputType^>
        echo     ^<TargetFramework^>net6.0-windows^</TargetFramework^>
        echo     ^<UseWPF^>true^</UseWPF^>
        echo   ^</PropertyGroup^>
        echo ^</Project^>
    ) > WorkSpaceManager.csproj
    
    dotnet clean
    dotnet restore
    dotnet build
    
    if %errorlevel% equ 0 (
        echo.
        echo ===============================================
        echo SUCCESS: Minimal build completed!
        echo ===============================================
        goto :success
    ) else (
        goto :error
    )
)

:success
echo.
echo Final clean build completed successfully!
echo.
echo You now have:
echo - Single project file: WorkSpaceManager.csproj
echo - Working WPF application
echo - No build conflicts
echo.
pause
exit /b 0

:error
echo.
echo ===============================================
echo Build failed completely.
echo ===============================================
echo.
echo Please check:
echo 1. .NET 6.0 SDK installation
echo 2. File permissions
echo 3. Antivirus interference
echo.
pause
exit /b 1
