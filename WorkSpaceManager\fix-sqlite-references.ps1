# PowerShell script to fix SQLite references
Write-Host "Fixing SQLite references in all service files..."

# Get all C# files in Services directory
$serviceFiles = Get-ChildItem -Path "Services" -Filter "*.cs" -Recurse

foreach ($file in $serviceFiles) {
    Write-Host "Processing: $($file.Name)"
    
    # Read file content
    $content = Get-Content $file.FullName -Raw
    
    # Replace System.Data.SQLite with Microsoft.Data.Sqlite
    $content = $content -replace "using System\.Data\.SQLite;", "using Microsoft.Data.Sqlite;"
    $content = $content -replace "SQLiteConnection", "SqliteConnection"
    $content = $content -replace "SQLiteCommand", "SqliteCommand"
    
    # Write back to file
    Set-Content -Path $file.FullName -Value $content -NoNewline
    
    Write-Host "Fixed: $($file.Name)"
}

Write-Host "All SQLite references have been updated!"
Write-Host "You can now run 'dotnet restore' and 'dotnet build'"
