@echo off
echo ===============================================
echo    إصلاح ملفات XAML - إزالة Material Design
echo ===============================================
echo.

echo جاري إصلاح ملفات XAML...

REM Backup original files
if not exist "Backup" mkdir "Backup"
copy "Resources\Styles\CardStyles.xaml" "Backup\" >nul 2>&1
copy "Views\*.xaml" "Backup\" >nul 2>&1

REM Remove problematic files
echo إزالة ملفات الأنماط المشكلة...
if exist "Resources\Styles\CardStyles.xaml" del "Resources\Styles\CardStyles.xaml"
if exist "Resources\Styles\ButtonStyles.xaml" del "Resources\Styles\ButtonStyles.xaml"
if exist "Resources\Styles\TextStyles.xaml" del "Resources\Styles\TextStyles.xaml"

REM Create simple MainWindow.xaml
echo إنشاء MainWindow.xaml مبسط...
(
echo ^<Window x:Class="WorkSpaceManager.Views.MainWindow"
echo         xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
echo         xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
echo         Title="Work Space Manager - نظام إدارة المساحات المشتركة" 
echo         Height="800" Width="1200"
echo         WindowStartupLocation="CenterScreen"
echo         FlowDirection="RightToLeft"
echo         FontFamily="Segoe UI"^>
echo.
echo     ^<Grid^>
echo         ^<Grid.RowDefinitions^>
echo             ^<RowDefinition Height="Auto"/^>
echo             ^<RowDefinition Height="*"/^>
echo             ^<RowDefinition Height="Auto"/^>
echo         ^</Grid.RowDefinitions^>
echo.
echo         ^<!-- Header --^>
echo         ^<Border Grid.Row="0" Background="#2196F3" Padding="20"^>
echo             ^<Grid^>
echo                 ^<Grid.ColumnDefinitions^>
echo                     ^<ColumnDefinition Width="*"/^>
echo                     ^<ColumnDefinition Width="Auto"/^>
echo                 ^</Grid.ColumnDefinitions^>
echo.
echo                 ^<TextBlock Grid.Column="0" Text="Work Space Manager" 
echo                          FontSize="24" FontWeight="Bold" Foreground="White"/^>
echo.
echo                 ^<StackPanel Grid.Column="1" Orientation="Horizontal"^>
echo                     ^<TextBlock x:Name="TxtCurrentShift" Text="لا يوجد شيفت نشط" 
echo                              Foreground="White" Margin="0,0,20,0"/^>
echo                     ^<Button x:Name="BtnThemeToggle" Content="🌙 الوضع الليلي" 
echo                           Background="Transparent" Foreground="White" BorderThickness="1"
echo                           Click="BtnThemeToggle_Click"/^>
echo                 ^</StackPanel^>
echo             ^</Grid^>
echo         ^</Border^>
echo.
echo         ^<!-- Navigation and Content --^>
echo         ^<Grid Grid.Row="1"^>
echo             ^<Grid.ColumnDefinitions^>
echo                 ^<ColumnDefinition Width="200"/^>
echo                 ^<ColumnDefinition Width="*"/^>
echo             ^</Grid.ColumnDefinitions^>
echo.
echo             ^<!-- Navigation Menu --^>
echo             ^<Border Grid.Column="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,1,0"^>
echo                 ^<StackPanel Margin="10"^>
echo                     ^<Button x:Name="BtnDashboard" Content="🏠 الرئيسية" 
echo                           Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
echo                           Click="BtnDashboard_Click"/^>
echo                     ^<Button x:Name="BtnCustomers" Content="👥 العملاء" 
echo                           Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
echo                           Click="BtnCustomers_Click"/^>
echo                     ^<Button x:Name="BtnSessions" Content="⏰ الجلسات" 
echo                           Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
echo                           Click="BtnSessions_Click"/^>
echo                     ^<Button x:Name="BtnProducts" Content="🛍️ المنتجات" 
echo                           Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
echo                           Click="BtnProducts_Click"/^>
echo                     ^<Button x:Name="BtnInvoices" Content="🧾 الفواتير" 
echo                           Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
echo                           Click="BtnInvoices_Click"/^>
echo                     ^<Button x:Name="BtnShifts" Content="🔄 الشيفتات" 
echo                           Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
echo                           Click="BtnShifts_Click"/^>
echo                     ^<Button x:Name="BtnReports" Content="📊 التقارير" 
echo                           Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
echo                           Click="BtnReports_Click"/^>
echo                     ^<Button x:Name="BtnSettings" Content="⚙️ الإعدادات" 
echo                           Margin="0,5" Padding="15,10" HorizontalAlignment="Stretch"
echo                           Click="BtnSettings_Click"/^>
echo                 ^</StackPanel^>
echo             ^</Border^>
echo.
echo             ^<!-- Main Content --^>
echo             ^<Frame x:Name="MainFrame" Grid.Column="1" NavigationUIVisibility="Hidden"/^>
echo         ^</Grid^>
echo.
echo         ^<!-- Status Bar --^>
echo         ^<Border Grid.Row="2" Background="#E0E0E0" Padding="10"^>
echo             ^<TextBlock x:Name="TxtShiftTime" Text="مرحباً بك في Work Space Manager"/^>
echo         ^</Border^>
echo     ^</Grid^>
echo ^</Window^>
) > "Views\MainWindow.xaml"

echo تم إنشاء MainWindow.xaml مبسط بنجاح!

REM Create simple DashboardPage.xaml
echo إنشاء DashboardPage.xaml مبسط...
(
echo ^<Page x:Class="WorkSpaceManager.Views.DashboardPage"
echo       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
echo       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
echo       Title="الرئيسية"
echo       FlowDirection="RightToLeft"^>
echo.
echo     ^<ScrollViewer VerticalScrollBarVisibility="Auto"^>
echo         ^<StackPanel Margin="20"^>
echo             ^<TextBlock Text="لوحة التحكم الرئيسية" 
echo                      FontSize="28" FontWeight="Bold" 
echo                      Foreground="#2196F3" Margin="0,0,0,20"/^>
echo.
echo             ^<TextBlock Text="مرحباً بك في نظام إدارة المساحات المشتركة" 
echo                      FontSize="16" Margin="0,0,0,20"/^>
echo.
echo             ^<Grid^>
echo                 ^<Grid.ColumnDefinitions^>
echo                     ^<ColumnDefinition Width="*"/^>
echo                     ^<ColumnDefinition Width="*"/^>
echo                 ^</Grid.ColumnDefinitions^>
echo.
echo                 ^<Border Grid.Column="0" Background="#E3F2FD" 
echo                       CornerRadius="5" Padding="20" Margin="0,0,10,0"^>
echo                     ^<StackPanel^>
echo                         ^<TextBlock Text="الإجراءات السريعة" 
echo                                  FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/^>
echo                         ^<Button x:Name="BtnStartSession" Content="🚀 بدء جلسة جديدة" 
echo                               Margin="0,5" Padding="15,10" Background="#4CAF50" Foreground="White"
echo                               Click="BtnStartSession_Click"/^>
echo                         ^<Button x:Name="BtnNewCustomer" Content="👤 إضافة عميل جديد" 
echo                               Margin="0,5" Padding="15,10" Background="#2196F3" Foreground="White"
echo                               Click="BtnNewCustomer_Click"/^>
echo                     ^</StackPanel^>
echo                 ^</Border^>
echo.
echo                 ^<Border Grid.Column="1" Background="#F5F5F5" 
echo                       CornerRadius="5" Padding="20" Margin="10,0,0,0"^>
echo                     ^<StackPanel^>
echo                         ^<TextBlock Text="إحصائيات سريعة" 
echo                                  FontWeight="Bold" FontSize="16" Margin="0,0,0,15"/^>
echo                         ^<TextBlock x:Name="TxtActiveSessions" Text="الجلسات النشطة: 0"/^>
echo                         ^<TextBlock x:Name="TxtTodaySales" Text="مبيعات اليوم: 0.00 ريال"/^>
echo                     ^</StackPanel^>
echo                 ^</Border^>
echo             ^</Grid^>
echo         ^</StackPanel^>
echo     ^</ScrollViewer^>
echo ^</Page^>
) > "Views\DashboardPage.xaml"

echo تم إنشاء DashboardPage.xaml مبسط بنجاح!

echo.
echo ===============================================
echo تم إصلاح جميع ملفات XAML بنجاح!
echo.
echo يمكنك الآن تشغيل:
echo   dotnet build WorkSpaceManagerSimple.csproj
echo ===============================================
echo.
pause
