﻿#pragma checksum "..\..\..\..\Backup\EditProductWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9836A5924C4D1391582EE1C7FA3B50195C38DFEA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WorkSpaceManager.Views {
    
    
    /// <summary>
    /// EditProductWindow
    /// </summary>
    public partial class EditProductWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtProductId;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtProductName;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbCategory;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtPrice;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCost;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtQuantity;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBarcode;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkIsActive;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDescription;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtProfitMargin;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtProfitPercentage;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Backup\EditProductWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WorkSpaceManager;component/backup/editproductwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Backup\EditProductWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "6.0.36.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtProductId = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.TxtProductName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CmbCategory = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.TxtPrice = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TxtCost = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TxtQuantity = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            
            #line 86 "..\..\..\..\Backup\EditProductWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnUpdateQuantity_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TxtBarcode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            
            #line 108 "..\..\..\..\Backup\EditProductWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnGenerateBarcode_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ChkIsActive = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.TxtDescription = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.TxtProfitMargin = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtProfitPercentage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\Backup\EditProductWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\Backup\EditProductWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

