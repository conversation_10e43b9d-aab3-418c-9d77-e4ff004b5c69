@echo off
echo ===============================================
echo    Work Space Manager - Run Clean Build
echo ===============================================
echo.

REM Check if project is built
if not exist "bin\Release" (
    echo Project not built. Building now...
    call clean-build.bat
    if %errorlevel% neq 0 (
        echo Build failed
        pause
        exit /b 1
    )
)

echo Running the application...
dotnet run

if %errorlevel% neq 0 (
    echo.
    echo Failed to run. Trying alternative...
    if exist "bin\Release\net6.0-windows\WorkSpaceManager.exe" (
        echo Running executable directly...
        start bin\Release\net6.0-windows\WorkSpaceManager.exe
    ) else (
        echo No executable found. Please run clean-build.bat first.
        pause
        exit /b 1
    )
)

echo.
echo Application started successfully!
pause
