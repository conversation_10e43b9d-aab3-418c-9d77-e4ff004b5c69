@echo off
echo ===============================================
echo    Work Space Manager - Run Application
echo ===============================================
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: .NET SDK not installed or not in PATH
    echo You can download it from: https://dotnet.microsoft.com/download
    echo.
    echo Trying to run from published files...
    
    REM Try to run from publish folder
    if exist "publish\WorkSpaceManager.exe" (
        echo Found executable, running...
        cd publish
        start WorkSpaceManager.exe
        echo Application started successfully!
        goto :end
    ) else (
        echo Executable not found
        echo Please build the project first using build-english.bat
        goto :error
    )
)

echo .NET SDK found
echo.

REM Check if project is built
if not exist "bin\Release" (
    echo Project not built, building now...
    call build-english.bat
    if %errorlevel% neq 0 (
        echo Build failed
        goto :error
    )
)

echo Running the application...

REM Try different project files
if exist "WorkSpaceManagerClean.csproj" (
    dotnet run --project WorkSpaceManagerClean.csproj --configuration Release
) else if exist "WorkSpaceManagerMinimal.csproj" (
    dotnet run --project WorkSpaceManagerMinimal.csproj --configuration Release
) else if exist "WorkSpaceManager.csproj" (
    dotnet run --project WorkSpaceManager.csproj --configuration Release
) else (
    echo No valid project file found
    goto :error
)

goto :end

:error
echo.
echo ===============================================
echo Error running the application
echo.
echo Suggested solutions:
echo 1. Install .NET 6.0 SDK
echo 2. Run build-english.bat to build the project
echo 3. Check that all files are present
echo ===============================================
pause
exit /b 1

:end
echo.
echo ===============================================
echo Thank you for using Work Space Manager
echo ===============================================
pause
