@echo off
echo ===============================================
echo    Work Space Manager - Run Final Build
echo ===============================================
echo.

REM Check if WorkSpaceManager.csproj exists
if not exist "WorkSpaceManager.csproj" (
    echo ERROR: WorkSpaceManager.csproj not found
    echo Please run final-clean-build.bat first
    pause
    exit /b 1
)

REM Check if project is built
if not exist "bin\Release" (
    echo Project not built. Building now...
    call final-clean-build.bat
    if %errorlevel% neq 0 (
        echo Build failed
        pause
        exit /b 1
    )
)

echo Running the application...
echo Using project: WorkSpaceManager.csproj
echo.

dotnet run --project WorkSpaceManager.csproj

if %errorlevel% neq 0 (
    echo.
    echo Failed to run with dotnet run. Trying alternatives...
    
    REM Try running the executable directly
    if exist "bin\Release\net6.0-windows\WorkSpaceManager.exe" (
        echo Running executable directly...
        start bin\Release\net6.0-windows\WorkSpaceManager.exe
        echo Application started!
    ) else if exist "bin\Debug\net6.0-windows\WorkSpaceManager.exe" (
        echo Running debug executable...
        start bin\Debug\net6.0-windows\WorkSpaceManager.exe
        echo Application started!
    ) else (
        echo No executable found. Building first...
        dotnet build WorkSpaceManager.csproj --configuration Release
        if exist "bin\Release\net6.0-windows\WorkSpaceManager.exe" (
            start bin\Release\net6.0-windows\WorkSpaceManager.exe
            echo Application started!
        ) else (
            echo Failed to create executable
            pause
            exit /b 1
        )
    )
) else (
    echo Application started successfully!
)

echo.
pause
