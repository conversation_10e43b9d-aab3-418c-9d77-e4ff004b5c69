[Setup]
; Basic Information
AppName=Work Space Manager
AppVersion=1.0.0
AppPublisher=Work Space Solutions
AppPublisherURL=https://www.workspacemanager.com
AppSupportURL=https://www.workspacemanager.com/support
AppUpdatesURL=https://www.workspacemanager.com/updates
AppCopyright=Copyright (C) 2024 Work Space Solutions

; Installation Settings
DefaultDirName={autopf}\Work Space Manager
DefaultGroupName=Work Space Manager
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=installer
OutputBaseFilename=WorkSpaceManager_Setup_v1.0.0
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; System Requirements
MinVersion=10.0
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; Privileges
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog

; Languages
ShowLanguageDialog=no

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

[Files]
; Main Application Files
Source: "publish\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
Name: "{group}\Work Space Manager"; Filename: "{app}\WorkSpaceManager.exe"
Name: "{group}\{cm:ProgramOnTheWeb,Work Space Manager}"; Filename: "https://www.workspacemanager.com"
Name: "{group}\{cm:UninstallProgram,Work Space Manager}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Work Space Manager"; Filename: "{app}\WorkSpaceManager.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\Work Space Manager"; Filename: "{app}\WorkSpaceManager.exe"; Tasks: quicklaunchicon

[Run]
Filename: "{app}\WorkSpaceManager.exe"; Description: "{cm:LaunchProgram,Work Space Manager}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}\Data\Logs"
Type: files; Name: "{app}\Data\*.tmp"

[Code]
function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  Result := 0;
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // Check Windows version (Windows 10 or later)
  if Version.Major < 10 then
  begin
    MsgBox('This application requires Windows 10 or later.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  Result := True;
end;

procedure InitializeWizard();
begin
  // Custom initialization if needed
end;

function PrepareToInstall(var NeedsRestart: Boolean): String;
begin
  // Check if .NET 6.0 is installed
  if not RegKeyExists(HKLM, 'SOFTWARE\WOW6432Node\dotnet\Setup\InstalledVersions\x64\sharedhost') and
     not RegKeyExists(HKLM, 'SOFTWARE\dotnet\Setup\InstalledVersions\x64\sharedhost') then
  begin
    Result := 'Microsoft .NET 6.0 Runtime is required but not installed. Please install it from https://dotnet.microsoft.com/download and run this installer again.';
    Exit;
  end;
  
  Result := '';
end;

[Messages]
; Arabic Messages
arabic.WelcomeLabel1=مرحباً بك في معالج تثبيت [name]
arabic.WelcomeLabel2=سيقوم هذا المعالج بتثبيت [name/ver] على جهاز الكمبيوتر الخاص بك.%n%nيُنصح بإغلاق جميع التطبيقات الأخرى قبل المتابعة.
arabic.ClickNext=انقر فوق التالي للمتابعة.
arabic.ClickInstall=انقر فوق تثبيت لبدء التثبيت.
arabic.ClickFinish=انقر فوق إنهاء لإغلاق المعالج.

[CustomMessages]
; Arabic Custom Messages
arabic.LaunchProgram=تشغيل %1
arabic.CreateDesktopIcon=إنشاء أيقونة على سطح المكتب
arabic.CreateQuickLaunchIcon=إنشاء أيقونة في شريط التشغيل السريع
arabic.ProgramOnTheWeb=%1 على الويب
arabic.UninstallProgram=إلغاء تثبيت %1
arabic.AdditionalIcons=أيقونات إضافية
arabic.OptionalFeatures=ميزات اختيارية
