# إصلاح مشكلة عدم ظهور أزرار المشتريات

## المشكلة
كانت نافذة إضافة المشتريات صغيرة جداً (350 بكسل ارتفاع) مما يجعل الأزرار مقطوعة من الأسفل.

## الحل المطبق
✅ تم زيادة ارتفاع النافذة من 350 إلى 450 بكسل في:
- `gui/sessions.py` - نافذة المشتريات في إدارة الجلسات
- `gui/dashboard.py` - نافذة المشتريات في الصفحة الرئيسية

## الأزرار الموجودة الآن
- **💾 إضافة المشترى** - لحفظ المشترى
- **❌ إلغاء** - لإلغاء العملية

## كيفية الاختبار

### 1. تشغيل التطبيق العادي:
```bash
python main.py
```

### 2. اختبار نافذة المشتريات:
```bash
python test_purchase_dialog.py
```

### 3. خطوات الاختبار في التطبيق:
1. اذهب إلى "إدارة الجلسات"
2. ابدأ جلسة جديدة لعميل
3. اضغط "إضافة مشترى"
4. يجب أن تظهر النافذة كاملة مع الأزرار في الأسفل

## النافذة الآن تحتوي على:
- معلومات الجلسة
- قائمة منسدلة للمنتجات
- حقل الكمية
- حقل السعر
- عرض الإجمالي
- منطقة الملاحظات
- **أزرار الحفظ والإلغاء (مرئية الآن)**

## إذا لم تظهر الأزرار:
1. تأكد من تشغيل التطبيق بعد التحديث
2. جرب إغلاق وإعادة فتح النافذة
3. تحقق من دقة الشاشة (قد تحتاج لزيادة الارتفاع أكثر)

## للمطورين:
إذا كنت تريد تعديل حجم النافذة أكثر، غير القيم في:
- السطر 556 في `gui/sessions.py`
- السطر 682 في `gui/dashboard.py`

من `"450x450"` إلى الحجم المطلوب.
