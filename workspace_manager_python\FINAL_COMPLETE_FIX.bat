@echo off
chcp 65001 >nul
echo ========================================
echo      الإصلاح النهائي الشامل
echo ========================================
echo.

echo 🔧 إصلاح قاعدة البيانات وإضافة البيانات التجريبية...
python fix_all_problems.py
echo.

echo 🧮 اختبار نظام الفواتير المحسن...
python test_complete_invoice_system.py
echo.

echo 🔍 اختبار جميع الإصلاحات الجديدة...
python test_all_fixes.py
echo.

echo ✅ تم الانتهاء من جميع الإصلاحات!
echo.
echo 🎯 الميزات الجديدة المضافة:
echo.
echo   📋 عرض تفاصيل الجلسات:
echo      ✅ زر "عرض التفاصيل" في صفحة الجلسات
echo      ✅ نافذة تفصيلية تعرض معلومات الجلسة والمشتريات
echo      ✅ حساب الإجمالي الكلي (جلسة + مشتريات)
echo.
echo   🛒 المشتريات في التكلفة الإجمالية:
echo      ✅ المشتريات تُحسب في إجمالي الجلسة النشطة
echo      ✅ المشتريات تُحسب في تاريخ الجلسات
echo      ✅ عرض التكلفة الصحيحة في جميع الصفحات
echo.
echo   🧾 تعديل الفواتير:
echo      ✅ زر "تعديل الفاتورة" في صفحة الفواتير
echo      ✅ إمكانية تعديل جميع مبالغ الفاتورة
echo      ✅ زر "حفظ التغييرات" يعمل بشكل صحيح
echo      ✅ تحديث حالة الدفع تلقائياً
echo.
echo   💰 صفحة المصروفات:
echo      ✅ عرض جميع المصروفات مع الإحصائيات
echo      ✅ إضافة وتعديل وحذف المصروفات
echo      ✅ فلترة المصروفات حسب النوع والبحث
echo      ✅ حساب إجمالي المصروفات الشهرية والكلية
echo.
echo   🎨 تحسينات إضافية:
echo      ✅ تعديل أسعار المنتجات يدوياً عند الشراء
echo      ✅ زر استعادة السعر الأصلي (↻)
echo      ✅ عرض الجلسات المفوترة مع التمييز
echo      ✅ إمكانية إصدار فواتير متعددة لنفس الجلسة
echo.

echo اضغط أي مفتاح لتشغيل التطبيق...
pause >nul

echo 🚀 تشغيل التطبيق...
python main.py
