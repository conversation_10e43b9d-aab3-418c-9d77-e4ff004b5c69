# ميزات التسعير والفواتير الجديدة

## الميزات المضافة

### ✅ 1. تعديل السعر يدوياً
- **في نافذة إضافة المشتريات**: يمكن تعديل سعر المنتج يدوياً
- **زر الاستعادة (↻)**: لاستعادة السعر الأصلي للمنتج
- **حساب تلقائي**: يتم حساب الإجمالي تلقائياً عند تغيير السعر أو الكمية

### ✅ 2. حساب المشتريات في الفاتورة
- **مبلغ الجلسة**: يتم حساب تكلفة الوقت المستخدم
- **مبلغ المشتريات**: يتم جمع جميع المشتريات المضافة للجلسة
- **الإجمالي**: مبلغ الجلسة + مبلغ المشتريات
- **الخصم**: يمكن إضافة خصم (مبلغ أو نسبة مئوية)
- **المبلغ النهائي**: الإجمالي - الخصم

### ✅ 3. تفاصيل الفاتورة المحسنة
- **عرض تفصيلي**: تظهر جميع المبالغ بوضوح
- **تحديث تلقائي**: عند اختيار جلسة تتحدث التفاصيل تلقائياً
- **رسائل تشخيصية**: لمساعدة في اكتشاف المشاكل

## كيفية الاستخدام

### 1. إضافة مشتريات بأسعار مخصصة:
1. اذهب إلى "إدارة الجلسات"
2. ابدأ جلسة جديدة أو اختر جلسة نشطة
3. اضغط "إضافة مشترى"
4. اختر المنتج من القائمة
5. **عدل السعر حسب الحاجة** في حقل "السعر"
6. أدخل الكمية
7. اضغط "💾 إضافة المشترى"

### 2. استعادة السعر الأصلي:
- اضغط زر **↻** بجانب حقل السعر لاستعادة السعر الأصلي للمنتج

### 3. إنشاء فاتورة شاملة:
1. أنهِ الجلسة أولاً
2. اضغط "إنشاء فاتورة"
3. ستظهر تفاصيل المبالغ:
   - **مبلغ الجلسة**: تكلفة الوقت
   - **مبلغ المشتريات**: إجمالي المشتريات
   - **الإجمالي قبل الخصم**: المجموع الكلي
4. أضف خصم إذا لزم الأمر
5. أدخل المبلغ المدفوع
6. اضغط "💾 إنشاء الفاتورة"

## أمثلة عملية

### مثال 1: جلسة مع مشتريات
- **جلسة**: 2 ساعة × 15 جنيه = 30 جنيه
- **مشتريات**: 
  - شاي × 2 = 10 جنيه
  - قهوة × 1 = 8 جنيه (سعر مخصص)
- **الإجمالي**: 30 + 10 + 8 = 48 جنيه
- **خصم**: 3 جنيه
- **المبلغ النهائي**: 48 - 3 = 45 جنيه

### مثال 2: تعديل سعر منتج
- **السعر الأصلي**: شاي = 5 جنيه
- **السعر المخصص**: 7 جنيه (زيادة للعميل المميز)
- **الحساب**: 2 × 7 = 14 جنيه بدلاً من 10 جنيه

## الاختبار

### اختبار شامل:
```bash
python test_invoice_calculations.py
```

### اختبار يدوي:
1. شغل التطبيق: `python main.py`
2. ابدأ جلسة جديدة
3. أضف مشتريات بأسعار مختلفة
4. أنهِ الجلسة وأنشئ فاتورة
5. تحقق من صحة الحسابات

## الملفات المعدلة

1. **gui/sessions.py**: إضافة زر استعادة السعر وتحسين واجهة المشتريات
2. **gui/dashboard.py**: نفس التحسينات في الصفحة الرئيسية
3. **gui/invoices.py**: تحسين عرض تفاصيل المبالغ في الفاتورة
4. **database/database_manager.py**: التأكد من حساب المشتريات بشكل صحيح

## ملاحظات مهمة

- ✅ **جميع الحسابات دقيقة**: تم اختبار الحسابات والتأكد من صحتها
- ✅ **مرونة في التسعير**: يمكن تعديل أي سعر حسب الحاجة
- ✅ **شفافية في الفواتير**: جميع المبالغ واضحة ومفصلة
- ✅ **سهولة الاستخدام**: واجهة بسيطة ومفهومة

## في حالة وجود مشاكل

1. تأكد من تشغيل `fix_all_problems.py` أولاً
2. اختبر باستخدام `test_invoice_calculations.py`
3. تحقق من رسائل الخطأ في وحدة التحكم
4. تأكد من وجود منتجات وعملاء في قاعدة البيانات
