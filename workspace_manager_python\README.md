# 🏢 Work Space Manager - الإصدار 2.0 🐍

## نظام إدارة مساحة العمل الكامل - محدث ومحسن!

### 🔥 الجديد في الإصدار 2.1:
- 💰 **العملة المصرية**: تحديث كامل للجنيه المصري
- 📱 **أرقام مصرية**: دعم كامل للأرقام المصرية
- ⚡ **إضافة سريعة**: عميل جديد + جلسة في ثوانٍ
- 🏠 **بدء من الرئيسية**: قائمة العملاء المسجلين لبدء جلسات سريعة
- ✏️ **تعديل شامل**: تعديل وحذف العملاء والجلسات والمنتجات
- 🛒 **نظام مشتريات**: إضافة مشتريات للجلسات مع تحديث المخزون
- 💳 **فواتير متقدمة**: خصومات، دفعات جزئية، طباعة
- 📊 **تقارير شاملة**: جرد يومي/أسبوعي/شهري مع إحصائيات مرئية

### 🎯 المميزات:
- 👥 إدارة العملاء
- ⏰ إدارة الجلسات والوقت
- 🛍️ إدارة المنتجات والمشروبات
- 🧾 نظام الفواتير
- 📊 التقارير والإحصائيات
- 🗄️ قاعدة بيانات SQLite مدمجة

### 🚀 التثبيت والتشغيل:

#### المتطلبات:
```bash
# Python 3.6+ (مدمج في معظم الأنظمة)
python --version
```

#### التشغيل السريع:
```bash
# الطريقة الأولى: تشغيل مباشر
python main.py

# الطريقة الثانية: استخدام ملف التشغيل (Windows)
run.bat

# الطريقة الثالثة: اختبار التطبيق أولاً
python test_app.py
```

#### التثبيت المفصل:
1. **تحميل Python** (إذا لم يكن مثبتاً):
   - زيارة: https://www.python.org/downloads/
   - تحميل Python 3.6 أو أحدث
   - التأكد من إضافة Python إلى PATH

2. **تشغيل التطبيق**:
   ```bash
   cd workspace_manager_python
   python main.py
   ```

3. **في حالة وجود مشاكل**:
   ```bash
   # اختبار التطبيق
   python test_app.py

   # عرض المساعدة
   python main.py --help
   ```

### 📁 هيكل المشروع:
```
workspace_manager_python/
├── main.py                 # الملف الرئيسي
├── database/
│   ├── __init__.py
│   ├── database_manager.py # إدارة قاعدة البيانات
│   └── models.py          # نماذج البيانات
├── gui/
│   ├── __init__.py
│   ├── main_window.py     # النافذة الرئيسية
│   ├── customers.py       # واجهة العملاء
│   ├── products.py        # واجهة المنتجات
│   ├── sessions.py        # واجهة الجلسات
│   ├── invoices.py        # واجهة الفواتير
│   └── dashboard.py       # لوحة التحكم
├── utils/
│   ├── __init__.py
│   ├── helpers.py         # دوال مساعدة
│   └── config.py          # إعدادات التطبيق
└── data/
    └── workspace.db       # قاعدة البيانات (تُنشأ تلقائياً)
```

### 🎨 الواجهة:
- تصميم عربي جميل ومرن
- ألوان عصرية ومريحة للعين
- سهولة في الاستخدام
- دعم كامل للغة العربية

### 🔧 الوظائف:
1. **إدارة العملاء**: إضافة، تعديل، حذف، بحث
2. **إدارة المنتجات**: أسعار، كميات، فئات
3. **إدارة الجلسات**: بدء، إيقاف، حساب التكلفة
4. **الفواتير**: إنشاء وطباعة فواتير احترافية
5. **التقارير**: إحصائيات مفصلة ومفيدة

### 📞 الدعم:
- لا يحتاج تثبيت معقد
- يعمل على Windows, Mac, Linux
- قاعدة بيانات محلية آمنة
- نسخ احتياطي تلقائي

---
**جاهز للعمل في دقائق! 🚀**
