#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def add_products():
    """إضافة منتجات بسيطة"""
    
    # مسار قاعدة البيانات
    db_path = os.path.join(os.path.dirname(__file__), 'data', 'workspace.db')
    
    if not os.path.exists(db_path):
        print("قاعدة البيانات غير موجودة!")
        return
    
    # الاتصال بقاعدة البيانات
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # التحقق من المنتجات الحالية
    cursor.execute("SELECT COUNT(*) FROM products WHERE is_active=1")
    count = cursor.fetchone()[0]
    print(f"عدد المنتجات الحالية: {count}")
    
    if count == 0:
        print("إضافة منتجات...")
        
        # منتجات تجريبية
        products = [
            ("شاي", "مشروبات ساخنة", 5.0, 2.0, 100, "", 1, "شاي أحمر ساخن"),
            ("قهوة تركية", "مشروبات ساخنة", 8.0, 3.0, 50, "", 1, "قهوة تركية أصلية"),
            ("عصير برتقال", "مشروبات باردة", 12.0, 5.0, 30, "", 1, "عصير برتقال طازج"),
            ("كولا", "مشروبات باردة", 10.0, 4.0, 40, "", 1, "مشروب غازي"),
            ("ساندويتش جبنة", "طعام", 15.0, 8.0, 20, "", 1, "ساندويتش جبنة مشوية"),
            ("بسكويت", "حلويات", 3.0, 1.5, 60, "", 1, "بسكويت محلى")
        ]
        
        for product in products:
            cursor.execute('''
                INSERT INTO products (name, category, price, cost, quantity, barcode, is_active, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', product)
            print(f"تم إضافة: {product[0]}")
        
        conn.commit()
        print("تم حفظ المنتجات")
    
    # عرض المنتجات
    cursor.execute("SELECT name, price, quantity FROM products WHERE is_active=1")
    products = cursor.fetchall()
    print(f"\nالمنتجات المتاحة ({len(products)}):")
    for product in products:
        print(f"  - {product[0]}: {product[1]} جنيه (متاح: {product[2]})")
    
    conn.close()
    print("\nتم الانتهاء!")

if __name__ == "__main__":
    add_products()
