#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح شامل لجميع مشاكل التطبيق
"""

import sys
import os
import sqlite3

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_database_and_add_products():
    """إصلاح قاعدة البيانات وإضافة المنتجات"""
    
    print("🔧 إصلاح قاعدة البيانات وإضافة المنتجات...")
    
    try:
        # مسار قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data', 'workspace.db')
        
        if not os.path.exists(db_path):
            print("❌ قاعدة البيانات غير موجودة!")
            return False
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # 1. حذف جميع المنتجات الموجودة لإعادة البدء
        print("🗑️ حذف المنتجات القديمة...")
        cursor.execute("DELETE FROM products")
        
        # 2. إضافة منتجات جديدة
        print("📦 إضافة منتجات جديدة...")
        
        sample_products = [
            ("شاي", "مشروبات ساخنة", 5.0, 2.0, 100, "", 1, "شاي أحمر ساخن"),
            ("قهوة تركية", "مشروبات ساخنة", 8.0, 3.0, 50, "", 1, "قهوة تركية أصلية"),
            ("عصير برتقال", "مشروبات باردة", 12.0, 5.0, 30, "", 1, "عصير برتقال طازج"),
            ("كولا", "مشروبات باردة", 10.0, 4.0, 40, "", 1, "مشروب غازي"),
            ("ساندويتش جبنة", "طعام", 15.0, 8.0, 20, "", 1, "ساندويتش جبنة مشوية"),
            ("بسكويت", "حلويات", 3.0, 1.5, 60, "", 1, "بسكويت محلى"),
            ("مياه", "مشروبات باردة", 2.0, 1.0, 80, "", 1, "مياه معدنية"),
            ("عصير تفاح", "مشروبات باردة", 8.0, 3.5, 25, "", 1, "عصير تفاح طبيعي"),
            ("كيك شوكولاتة", "حلويات", 20.0, 12.0, 15, "", 1, "كيك شوكولاتة لذيذ"),
            ("فطيرة جبنة", "طعام", 12.0, 6.0, 25, "", 1, "فطيرة جبنة ساخنة")
        ]
        
        for product in sample_products:
            cursor.execute('''
                INSERT INTO products (name, category, price, cost, quantity, barcode, is_active, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', product)
            print(f"  ✅ تم إضافة: {product[0]} - {product[1]} - {product[2]} جنيه")
        
        conn.commit()
        
        # 3. التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM products WHERE is_active=1")
        active_count = cursor.fetchone()[0]
        print(f"📊 إجمالي المنتجات النشطة: {active_count}")
        
        # 4. إضافة عميل تجريبي إذا لم يوجد
        cursor.execute("SELECT COUNT(*) FROM customers WHERE is_active=1")
        customers_count = cursor.fetchone()[0]
        
        if customers_count == 0:
            print("👤 إضافة عميل تجريبي...")
            cursor.execute('''
                INSERT INTO customers (name, phone, email, notes, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ("عميل تجريبي", "01234567890", "<EMAIL>", "عميل للاختبار", 1))
            conn.commit()
            print("  ✅ تم إضافة عميل تجريبي")
        
        conn.close()
        print("✅ تم إصلاح قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_application_functions():
    """اختبار وظائف التطبيق"""
    
    print("\n🧪 اختبار وظائف التطبيق...")
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # 1. اختبار المنتجات
        print("\n📦 اختبار المنتجات...")
        products = db.get_products()
        active_products = [p for p in products if p.is_active and p.quantity > 0]
        print(f"المنتجات المتاحة: {len(active_products)}")
        
        if not active_products:
            print("❌ لا توجد منتجات متاحة!")
            return False
        
        for i, product in enumerate(active_products[:3]):  # عرض أول 3 منتجات
            print(f"  {i+1}. {product.name}: {product.price} جنيه (متاح: {product.quantity})")
        
        # 2. اختبار العملاء
        print("\n👥 اختبار العملاء...")
        customers = db.get_customers()
        active_customers = [c for c in customers if c.is_active]
        print(f"العملاء النشطين: {len(active_customers)}")
        
        if not active_customers:
            print("❌ لا توجد عملاء!")
            return False
        
        customer = active_customers[0]
        product = active_products[0]
        
        # 3. اختبار بدء جلسة
        print(f"\n🎮 اختبار بدء جلسة للعميل: {customer.name}")
        session_id = db.start_session(
            customer_id=customer.id,
            customer_name=customer.name,
            hourly_rate=15.0,
            daily_rate=0,
            pricing_type="hourly"
        )
        print(f"✅ تم بدء الجلسة: ID={session_id}")
        
        # 4. اختبار إضافة مشترى
        print(f"\n🛒 اختبار إضافة مشترى: {product.name}")
        purchase_id = db.add_purchase(
            session_id=session_id,
            product_id=product.id,
            product_name=product.name,
            quantity=2,
            unit_price=product.price,
            notes="مشترى تجريبي"
        )
        print(f"✅ تم إضافة المشترى: ID={purchase_id}")
        
        # 5. اختبار الحصول على مشتريات الجلسة
        print("\n📋 اختبار الحصول على مشتريات الجلسة...")
        purchases = db.get_session_purchases(session_id)
        print(f"عدد المشتريات: {len(purchases)}")
        
        if purchases:
            purchase = purchases[0]
            print(f"  المشترى: {purchase.product_name} x {purchase.quantity} = {purchase.total_price} جنيه")
        
        # 6. اختبار إنهاء الجلسة
        print("\n🏁 اختبار إنهاء الجلسة...")
        ended_session = db.end_session(session_id)
        if ended_session:
            print(f"✅ تم إنهاء الجلسة بنجاح!")
            print(f"   المدة: {ended_session.total_hours:.2f} ساعة")
            print(f"   التكلفة: {ended_session.total_amount:.2f} جنيه")
        else:
            print("❌ فشل في إنهاء الجلسة")
            return False
        
        print("\n🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء الإصلاح الشامل للتطبيق...")
    print("="*60)
    
    # 1. إصلاح قاعدة البيانات وإضافة المنتجات
    db_success = fix_database_and_add_products()
    
    # 2. اختبار وظائف التطبيق
    app_success = test_application_functions() if db_success else False
    
    # 3. عرض النتائج
    print("\n" + "="*60)
    print("📋 ملخص النتائج:")
    print(f"  قاعدة البيانات: {'✅ تم الإصلاح' if db_success else '❌ فشل الإصلاح'}")
    print(f"  وظائف التطبيق: {'✅ تعمل بشكل صحيح' if app_success else '❌ توجد مشاكل'}")
    
    if db_success and app_success:
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("\n📝 يمكنك الآن:")
        print("  1. تشغيل التطبيق: python main.py")
        print("  2. إضافة مشتريات للعملاء")
        print("  3. إنهاء الجلسات بدون أخطاء")
        print("  4. إنشاء الفواتير")
        print("  5. إضافة وتعديل المنتجات")
    else:
        print("\n⚠️ توجد مشاكل تحتاج إلى إصلاح إضافي.")
        print("يرجى مراجعة رسائل الخطأ أعلاه.")

if __name__ == "__main__":
    main()
