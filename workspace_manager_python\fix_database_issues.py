#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح مشاكل قاعدة البيانات
"""

import sys
import os
import sqlite3

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_database_issues():
    """إصلاح مشاكل قاعدة البيانات"""
    
    print("🔧 بدء إصلاح مشاكل قاعدة البيانات...")
    
    try:
        # الاتصال المباشر بقاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'data', 'workspace.db')
        
        if not os.path.exists(db_path):
            print("❌ قاعدة البيانات غير موجودة!")
            return False
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # 1. التحقق من هيكل جدول purchases
        print("\n📋 فحص هيكل جدول purchases...")
        cursor.execute("PRAGMA table_info(purchases)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        print(f"الأعمدة الموجودة: {column_names}")
        
        # 2. إضافة منتجات تجريبية إذا لم توجد
        print("\n📦 فحص المنتجات...")
        cursor.execute("SELECT COUNT(*) FROM products WHERE is_active=1")
        active_products_count = cursor.fetchone()[0]
        print(f"عدد المنتجات النشطة: {active_products_count}")
        
        if active_products_count == 0:
            print("إضافة منتجات تجريبية...")
            
            # منتجات تجريبية
            sample_products = [
                ("شاي", "مشروبات ساخنة", 5.0, 2.0, 100, "", 1, "شاي أحمر ساخن"),
                ("قهوة تركية", "مشروبات ساخنة", 8.0, 3.0, 50, "", 1, "قهوة تركية أصلية"),
                ("عصير برتقال", "مشروبات باردة", 12.0, 5.0, 30, "", 1, "عصير برتقال طازج"),
                ("كولا", "مشروبات باردة", 10.0, 4.0, 40, "", 1, "مشروب غازي"),
                ("ساندويتش جبنة", "طعام", 15.0, 8.0, 20, "", 1, "ساندويتش جبنة مشوية"),
                ("بسكويت", "حلويات", 3.0, 1.5, 60, "", 1, "بسكويت محلى"),
                ("مياه", "مشروبات باردة", 2.0, 1.0, 80, "", 1, "مياه معدنية"),
                ("عصير تفاح", "مشروبات باردة", 8.0, 3.5, 25, "", 1, "عصير تفاح طبيعي")
            ]
            
            for product in sample_products:
                cursor.execute('''
                    INSERT INTO products (name, category, price, cost, quantity, barcode, is_active, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', product)
                print(f"  ✅ تم إضافة: {product[0]}")
            
            conn.commit()
            print("تم حفظ المنتجات في قاعدة البيانات")
        
        # 3. التحقق من النتيجة النهائية
        cursor.execute("SELECT COUNT(*) FROM products WHERE is_active=1")
        final_active_products = cursor.fetchone()[0]
        print(f"\n📊 إجمالي المنتجات النشطة: {final_active_products}")
        
        # 4. عرض المنتجات المتاحة
        cursor.execute("SELECT name, price, quantity FROM products WHERE is_active=1 AND quantity > 0")
        available_products = cursor.fetchall()
        print(f"\n🛍️ المنتجات المتاحة للشراء ({len(available_products)}):")
        for product in available_products:
            print(f"  - {product[0]}: {product[1]} جنيه (متاح: {product[2]})")
        
        # 5. التحقق من وجود عملاء
        print("\n👥 فحص العملاء...")
        cursor.execute("SELECT COUNT(*) FROM customers WHERE is_active=1")
        customers_count = cursor.fetchone()[0]
        print(f"عدد العملاء النشطين: {customers_count}")
        
        if customers_count == 0:
            print("إضافة عميل تجريبي...")
            cursor.execute('''
                INSERT INTO customers (name, phone, email, notes, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ("عميل تجريبي", "01234567890", "<EMAIL>", "عميل للاختبار", 1))
            conn.commit()
            print("  ✅ تم إضافة عميل تجريبي")
        
        conn.close()
        
        print("\n🎉 تم إصلاح جميع مشاكل قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_purchases_functionality():
    """اختبار وظائف المشتريات"""
    
    print("\n🧪 اختبار وظائف المشتريات...")
    
    try:
        from database.database_manager import DatabaseManager
        from database.models import Customer
        
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # الحصول على المنتجات
        products = db.get_products()
        active_products = [p for p in products if p.is_active and p.quantity > 0]
        print(f"المنتجات المتاحة: {len(active_products)}")
        
        if not active_products:
            print("❌ لا توجد منتجات متاحة!")
            return False
        
        # الحصول على العملاء
        customers = db.get_customers()
        if not customers:
            print("❌ لا توجد عملاء!")
            return False
        
        customer = customers[0]
        product = active_products[0]
        
        print(f"العميل المختار: {customer.name}")
        print(f"المنتج المختار: {product.name}")
        
        # بدء جلسة تجريبية
        print("بدء جلسة تجريبية...")
        session_id = db.start_session(
            customer_id=customer.id,
            customer_name=customer.name,
            hourly_rate=15.0,
            daily_rate=0,
            pricing_type="hourly"
        )
        print(f"تم بدء الجلسة: ID={session_id}")
        
        # إضافة مشترى تجريبي
        print("إضافة مشترى تجريبي...")
        purchase_id = db.add_purchase(
            session_id=session_id,
            product_id=product.id,
            product_name=product.name,
            quantity=2,
            unit_price=product.price,
            notes="مشترى تجريبي"
        )
        print(f"تم إضافة المشترى: ID={purchase_id}")
        
        # الحصول على مشتريات الجلسة
        purchases = db.get_session_purchases(session_id)
        print(f"عدد المشتريات في الجلسة: {len(purchases)}")
        
        # إنهاء الجلسة
        print("إنهاء الجلسة...")
        ended_session = db.end_session(session_id)
        if ended_session:
            print(f"✅ تم إنهاء الجلسة بنجاح!")
            print(f"   المدة: {ended_session.total_hours:.2f} ساعة")
            print(f"   التكلفة: {ended_session.total_amount:.2f} جنيه")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المشتريات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إصلاح شامل للتطبيق...")
    
    # إصلاح قاعدة البيانات
    db_fixed = fix_database_issues()
    
    # اختبار وظائف المشتريات
    purchases_working = test_purchases_functionality()
    
    print("\n" + "="*60)
    print("📋 ملخص النتائج:")
    print(f"  قاعدة البيانات: {'✅ تم الإصلاح' if db_fixed else '❌ فشل الإصلاح'}")
    print(f"  وظائف المشتريات: {'✅ تعمل بشكل صحيح' if purchases_working else '❌ توجد مشاكل'}")
    
    if db_fixed and purchases_working:
        print("\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("يمكنك الآن تشغيل التطبيق واستخدام جميع الوظائف.")
        print("\n📝 للتشغيل:")
        print("  python main.py")
    else:
        print("\n⚠️ توجد مشاكل تحتاج إلى إصلاح إضافي.")

if __name__ == "__main__":
    main()
