"""
واجهة إدارة العملاء
Customers Management Interface
"""

import tkinter as tk
from tkinter import ttk
from database.models import Customer
from utils.config import FONTS
from utils.helpers import (
    show_success, show_error, show_warning, ask_confirmation,
    validate_email, validate_phone, format_datetime
)

class CustomersFrame:
    """إطار إدارة العملاء"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.selected_customer = None
        self.create_interface()
        self.refresh_customers()
    
    def create_interface(self):
        """إنشاء واجهة إدارة العملاء"""
        # عنوان الصفحة
        title_label = ttk.Label(
            self.frame, 
            text="👥 إدارة العملاء", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار البحث والأزرار
        self.create_search_section()
        
        # إطار قائمة العملاء
        self.create_customers_list()
        
        # إطار تفاصيل العميل
        self.create_customer_details()
    
    def create_search_section(self):
        """إنشاء قسم البحث والأزرار"""
        search_frame = ttk.Frame(self.frame)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # حقل البحث
        ttk.Label(search_frame, text="🔍 البحث:").pack(side=tk.LEFT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(search_frame)
        buttons_frame.pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="➕ إضافة عميل", 
            command=self.add_customer
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="✏️ تعديل", 
            command=self.edit_customer
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🗑️ حذف", 
            command=self.delete_customer
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🔄 تحديث", 
            command=self.refresh_customers
        ).pack(side=tk.LEFT, padx=2)
    
    def create_customers_list(self):
        """إنشاء قائمة العملاء"""
        list_frame = ttk.LabelFrame(self.frame, text="قائمة العملاء", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # جدول العملاء
        columns = {
            'id': {'text': 'الرقم', 'width': 60},
            'name': {'text': 'الاسم', 'width': 150},
            'phone': {'text': 'الهاتف', 'width': 120},
            'email': {'text': 'البريد الإلكتروني', 'width': 200},
            'registration_date': {'text': 'تاريخ التسجيل', 'width': 120},
            'status': {'text': 'الحالة', 'width': 80}
        }
        
        self.customers_tree = ttk.Treeview(list_frame)
        self.setup_treeview(self.customers_tree, columns)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=scrollbar.set)
        
        self.customers_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث التحديد
        self.customers_tree.bind('<<TreeviewSelect>>', self.on_customer_select)
        self.customers_tree.bind('<Double-1>', self.edit_customer)
    
    def create_customer_details(self):
        """إنشاء قسم تفاصيل العميل"""
        details_frame = ttk.LabelFrame(self.frame, text="تفاصيل العميل", padding=10)
        details_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # معلومات العميل
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X)
        
        # العمود الأول
        col1_frame = ttk.Frame(info_frame)
        col1_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col1_frame, text="الاسم:", font=FONTS['default']).pack(anchor=tk.W)
        self.name_label = ttk.Label(col1_frame, text="-", font=FONTS['heading'])
        self.name_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col1_frame, text="الهاتف:", font=FONTS['default']).pack(anchor=tk.W)
        self.phone_label = ttk.Label(col1_frame, text="-")
        self.phone_label.pack(anchor=tk.W, pady=(0, 10))
        
        # العمود الثاني
        col2_frame = ttk.Frame(info_frame)
        col2_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col2_frame, text="البريد الإلكتروني:", font=FONTS['default']).pack(anchor=tk.W)
        self.email_label = ttk.Label(col2_frame, text="-")
        self.email_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col2_frame, text="تاريخ التسجيل:", font=FONTS['default']).pack(anchor=tk.W)
        self.registration_label = ttk.Label(col2_frame, text="-")
        self.registration_label.pack(anchor=tk.W, pady=(0, 10))
        
        # الملاحظات
        ttk.Label(details_frame, text="الملاحظات:", font=FONTS['default']).pack(anchor=tk.W, pady=(10, 0))
        self.notes_text = tk.Text(details_frame, height=3, state=tk.DISABLED)
        self.notes_text.pack(fill=tk.X, pady=5)
        
        # أزرار الإجراءات السريعة
        actions_frame = ttk.Frame(details_frame)
        actions_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(
            actions_frame, 
            text="⏰ بدء جلسة", 
            command=self.start_session
        ).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            actions_frame, 
            text="📋 تاريخ العميل", 
            command=self.view_customer_history
        ).pack(side=tk.LEFT, padx=5)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def refresh_customers(self):
        """تحديث قائمة العملاء"""
        try:
            # مسح البيانات الحالية
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)
            
            # الحصول على العملاء
            search_term = self.search_var.get()
            customers = self.db.get_customers(search_term)
            
            # إضافة العملاء للجدول
            for customer in customers:
                status = "نشط" if customer.is_active else "غير نشط"
                self.customers_tree.insert('', 'end', values=(
                    customer.id,
                    customer.name,
                    customer.phone,
                    customer.email,
                    format_datetime(customer.registration_date, include_time=False),
                    status
                ))
                
        except Exception as e:
            show_error(f"خطأ في تحديث قائمة العملاء: {e}")
    
    def on_search_change(self, event=None):
        """عند تغيير نص البحث"""
        self.refresh_customers()
    
    def on_customer_select(self, event=None):
        """عند اختيار عميل"""
        selection = self.customers_tree.selection()
        if selection:
            item = self.customers_tree.item(selection[0])
            customer_id = item['values'][0]
            self.load_customer_details(customer_id)
    
    def load_customer_details(self, customer_id):
        """تحميل تفاصيل العميل"""
        try:
            customers = self.db.get_customers()
            customer = next((c for c in customers if c.id == customer_id), None)
            
            if customer:
                self.selected_customer = customer
                self.name_label.config(text=customer.name)
                self.phone_label.config(text=customer.phone or "-")
                self.email_label.config(text=customer.email or "-")
                self.registration_label.config(text=format_datetime(customer.registration_date, include_time=False))
                
                # تحديث الملاحظات
                self.notes_text.config(state=tk.NORMAL)
                self.notes_text.delete(1.0, tk.END)
                self.notes_text.insert(1.0, customer.notes or "لا توجد ملاحظات")
                self.notes_text.config(state=tk.DISABLED)
            else:
                self.clear_customer_details()
                
        except Exception as e:
            show_error(f"خطأ في تحميل تفاصيل العميل: {e}")
    
    def clear_customer_details(self):
        """مسح تفاصيل العميل"""
        self.selected_customer = None
        self.name_label.config(text="-")
        self.phone_label.config(text="-")
        self.email_label.config(text="-")
        self.registration_label.config(text="-")
        
        self.notes_text.config(state=tk.NORMAL)
        self.notes_text.delete(1.0, tk.END)
        self.notes_text.config(state=tk.DISABLED)
    
    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self.frame, "إضافة عميل جديد")
        if dialog.result:
            try:
                customer_id = self.db.add_customer(dialog.result)
                show_success("تم إضافة العميل بنجاح")
                self.refresh_customers()
            except Exception as e:
                show_error(f"خطأ في إضافة العميل: {e}")
    
    def edit_customer(self):
        """تعديل العميل المحدد"""
        if not self.selected_customer:
            show_warning("يرجى اختيار عميل للتعديل")
            return
        
        dialog = CustomerDialog(self.frame, "تعديل بيانات العميل", self.selected_customer)
        if dialog.result:
            try:
                self.db.update_customer(dialog.result)
                show_success("تم تحديث بيانات العميل بنجاح")
                self.refresh_customers()
                self.load_customer_details(dialog.result.id)
            except Exception as e:
                show_error(f"خطأ في تحديث العميل: {e}")
    
    def delete_customer(self):
        """حذف العميل المحدد"""
        if not self.selected_customer:
            show_warning("يرجى اختيار عميل للحذف")
            return
        
        if ask_confirmation(f"هل أنت متأكد من حذف العميل '{self.selected_customer.name}'؟"):
            try:
                self.db.delete_customer(self.selected_customer.id)
                show_success("تم حذف العميل بنجاح")
                self.refresh_customers()
                self.clear_customer_details()
            except Exception as e:
                show_error(f"خطأ في حذف العميل: {e}")
    
    def start_session(self):
        """بدء جلسة للعميل المحدد"""
        if not self.selected_customer:
            show_warning("يرجى اختيار عميل لبدء الجلسة")
            return
        
        # هنا يمكن فتح نافذة بدء الجلسة
        show_warning("سيتم تطوير هذه الميزة قريباً")
    
    def view_customer_history(self):
        """عرض تاريخ العميل"""
        if not self.selected_customer:
            show_warning("يرجى اختيار عميل لعرض تاريخه")
            return
        
        # هنا يمكن فتح نافذة تاريخ العميل
        show_warning("سيتم تطوير هذه الميزة قريباً")


class CustomerDialog:
    """نافذة حوار إضافة/تعديل العميل"""
    
    def __init__(self, parent, title, customer=None):
        self.result = None
        self.customer = customer
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_dialog()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تعبئة البيانات إذا كان تعديل
        if customer:
            self.populate_fields()
        
        # التركيز على حقل الاسم
        self.name_entry.focus()
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 400) // 2
        y = (self.dialog.winfo_screenheight() - 300) // 2
        self.dialog.geometry(f"400x300+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # حقول الإدخال
        ttk.Label(main_frame, text="الاسم *:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.name_entry = ttk.Entry(main_frame, width=30)
        self.name_entry.grid(row=0, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        
        ttk.Label(main_frame, text="الهاتف:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.phone_entry = ttk.Entry(main_frame, width=30)
        self.phone_entry.grid(row=1, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        
        ttk.Label(main_frame, text="البريد الإلكتروني:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.email_entry = ttk.Entry(main_frame, width=30)
        self.email_entry.grid(row=2, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        
        ttk.Label(main_frame, text="الملاحظات:").grid(row=3, column=0, sticky=tk.NW, pady=5)
        self.notes_text = tk.Text(main_frame, width=30, height=5)
        self.notes_text.grid(row=3, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        
        # حالة العميل
        self.active_var = tk.BooleanVar(value=True)
        self.active_check = ttk.Checkbutton(main_frame, text="عميل نشط", variable=self.active_var)
        self.active_check.grid(row=4, column=1, sticky=tk.W, pady=10, padx=(10, 0))
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="💾 حفظ", command=self.save_customer).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)
        
        # تكوين الأعمدة
        main_frame.columnconfigure(1, weight=1)
    
    def populate_fields(self):
        """تعبئة الحقول بالبيانات الحالية"""
        if self.customer:
            self.name_entry.insert(0, self.customer.name)
            self.phone_entry.insert(0, self.customer.phone or "")
            self.email_entry.insert(0, self.customer.email or "")
            self.notes_text.insert(1.0, self.customer.notes or "")
            self.active_var.set(self.customer.is_active)
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        # التحقق من صحة البيانات
        name = self.name_entry.get().strip()
        if not name:
            show_error("يرجى إدخال اسم العميل")
            return
        
        phone = self.phone_entry.get().strip()
        if phone and not validate_phone(phone):
            show_error("رقم الهاتف غير صحيح")
            return
        
        email = self.email_entry.get().strip()
        if email and not validate_email(email):
            show_error("البريد الإلكتروني غير صحيح")
            return
        
        # إنشاء كائن العميل
        customer = Customer(
            id=self.customer.id if self.customer else None,
            name=name,
            phone=phone,
            email=email,
            notes=self.notes_text.get(1.0, tk.END).strip(),
            is_active=self.active_var.get()
        )
        
        if self.customer:
            customer.registration_date = self.customer.registration_date
        
        self.result = customer
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
