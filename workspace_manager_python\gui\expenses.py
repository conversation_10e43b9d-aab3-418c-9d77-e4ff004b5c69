"""
واجهة إدارة مصروفات المكان
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from utils.config import FONTS
from utils.helpers import (
    show_success, show_error, show_warning, ask_confirmation,
    format_currency, format_datetime, safe_float_convert
)
from utils.scroll_helper import enable_scroll_for_all_children


class ExpensesPage:
    """صفحة إدارة مصروفات المكان"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.selected_expense = None
        
        self.create_interface()
        self.refresh_expenses()

        # إضافة دعم السكرول لجميع العناصر
        enable_scroll_for_all_children(self.frame)
    
    def create_interface(self):
        """إنشاء واجهة المصروفات"""
        # العنوان
        title_frame = ttk.Frame(self.frame)
        title_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(
            title_frame, 
            text="💰 إدارة مصروفات المكان", 
            font=FONTS['title']
        ).pack(side=tk.LEFT)
        
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(title_frame)
        buttons_frame.pack(side=tk.RIGHT)
        
        ttk.Button(
            buttons_frame, 
            text="➕ إضافة مصروف", 
            command=self.add_expense
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="✏️ تعديل", 
            command=self.edit_expense
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🗑️ حذف", 
            command=self.delete_expense
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🔄 تحديث", 
            command=self.refresh_expenses
        ).pack(side=tk.LEFT, padx=2)
        
        # إطار البحث والفلترة
        search_frame = ttk.LabelFrame(self.frame, text="البحث والفلترة", padding=10)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # البحث
        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT, padx=5)
        search_entry.bind('<KeyRelease>', self.filter_expenses)
        
        # فلترة حسب النوع
        ttk.Label(search_frame, text="النوع:").pack(side=tk.LEFT, padx=(20, 5))
        self.type_filter_var = tk.StringVar(value="الكل")
        type_combo = ttk.Combobox(search_frame, textvariable=self.type_filter_var, width=15, state="readonly")
        type_combo['values'] = ["الكل", "كهرباء", "مياه", "غاز", "إنترنت", "نظافة", "صيانة", "إيجار", "أخرى"]
        type_combo.pack(side=tk.LEFT, padx=5)
        type_combo.bind('<<ComboboxSelected>>', self.filter_expenses)
        
        # قائمة المصروفات
        list_frame = ttk.LabelFrame(self.frame, text="قائمة المصروفات", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء Treeview
        columns = ('id', 'type', 'description', 'amount', 'date', 'notes')
        self.expenses_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        self.expenses_tree.heading('id', text='الرقم')
        self.expenses_tree.heading('type', text='النوع')
        self.expenses_tree.heading('description', text='الوصف')
        self.expenses_tree.heading('amount', text='المبلغ')
        self.expenses_tree.heading('date', text='التاريخ')
        self.expenses_tree.heading('notes', text='ملاحظات')
        
        # تحديد عرض الأعمدة
        self.expenses_tree.column('id', width=80, anchor='center')
        self.expenses_tree.column('type', width=100, anchor='center')
        self.expenses_tree.column('description', width=200, anchor='w')
        self.expenses_tree.column('amount', width=120, anchor='center')
        self.expenses_tree.column('date', width=150, anchor='center')
        self.expenses_tree.column('notes', width=200, anchor='w')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.expenses_tree.yview)
        self.expenses_tree.configure(yscrollcommand=scrollbar.set)
        
        # تخطيط العناصر
        self.expenses_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط الأحداث
        self.expenses_tree.bind('<<TreeviewSelect>>', self.on_expense_select)
        self.expenses_tree.bind('<Double-1>', self.edit_expense)
        
        # إطار الإحصائيات
        stats_frame = ttk.LabelFrame(self.frame, text="إحصائيات المصروفات", padding=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # إجمالي المصروفات
        ttk.Label(stats_frame, text="إجمالي المصروفات:", font=FONTS['bold']).pack(side=tk.LEFT)
        self.total_expenses_label = ttk.Label(stats_frame, text="0.00 جنيه", font=FONTS['bold'], foreground="red")
        self.total_expenses_label.pack(side=tk.LEFT, padx=10)
        
        # مصروفات الشهر الحالي
        ttk.Label(stats_frame, text="مصروفات الشهر:", font=FONTS['bold']).pack(side=tk.LEFT, padx=(20, 5))
        self.monthly_expenses_label = ttk.Label(stats_frame, text="0.00 جنيه", font=FONTS['bold'], foreground="orange")
        self.monthly_expenses_label.pack(side=tk.LEFT, padx=10)
    
    def refresh_expenses(self):
        """تحديث قائمة المصروفات"""
        try:
            # مسح البيانات الحالية
            for item in self.expenses_tree.get_children():
                self.expenses_tree.delete(item)
            
            # الحصول على المصروفات
            expenses = self.db.get_expenses()
            
            total_amount = 0
            monthly_amount = 0
            current_month = datetime.now().strftime('%Y-%m')
            
            for expense in expenses:
                self.expenses_tree.insert('', 'end', values=(
                    expense.id,
                    expense.type,
                    expense.description,
                    format_currency(expense.amount),
                    format_datetime(expense.date),
                    expense.notes or ""
                ))
                
                total_amount += expense.amount
                
                # حساب مصروفات الشهر الحالي
                if expense.date.startswith(current_month):
                    monthly_amount += expense.amount
            
            # تحديث الإحصائيات
            self.total_expenses_label.config(text=format_currency(total_amount))
            self.monthly_expenses_label.config(text=format_currency(monthly_amount))
            
        except Exception as e:
            show_error(f"خطأ في تحديث المصروفات: {e}")
    
    def filter_expenses(self, event=None):
        """فلترة المصروفات"""
        try:
            search_term = self.search_var.get().lower()
            type_filter = self.type_filter_var.get()
            
            # مسح البيانات الحالية
            for item in self.expenses_tree.get_children():
                self.expenses_tree.delete(item)
            
            # الحصول على المصروفات
            expenses = self.db.get_expenses()
            
            total_amount = 0
            monthly_amount = 0
            current_month = datetime.now().strftime('%Y-%m')
            
            for expense in expenses:
                # تطبيق الفلاتر
                if type_filter != "الكل" and expense.type != type_filter:
                    continue
                
                if search_term and search_term not in expense.description.lower() and search_term not in expense.notes.lower():
                    continue
                
                self.expenses_tree.insert('', 'end', values=(
                    expense.id,
                    expense.type,
                    expense.description,
                    format_currency(expense.amount),
                    format_datetime(expense.date),
                    expense.notes or ""
                ))
                
                total_amount += expense.amount
                
                if expense.date.startswith(current_month):
                    monthly_amount += expense.amount
            
            # تحديث الإحصائيات
            self.total_expenses_label.config(text=format_currency(total_amount))
            self.monthly_expenses_label.config(text=format_currency(monthly_amount))
            
        except Exception as e:
            print(f"خطأ في فلترة المصروفات: {e}")
    
    def on_expense_select(self, event=None):
        """عند اختيار مصروف"""
        selection = self.expenses_tree.selection()
        if selection:
            item = self.expenses_tree.item(selection[0])
            expense_id = item['values'][0]
            
            # الحصول على تفاصيل المصروف
            expenses = self.db.get_expenses()
            self.selected_expense = next((e for e in expenses if e.id == expense_id), None)
    
    def add_expense(self):
        """إضافة مصروف جديد"""
        try:
            dialog = ExpenseDialog(self.frame, "إضافة مصروف جديد", self.db)
            if dialog.result:
                show_success("تم إضافة المصروف بنجاح")
                self.refresh_expenses()
        except Exception as e:
            show_error(f"خطأ في إضافة المصروف: {e}")
    
    def edit_expense(self):
        """تعديل المصروف المحدد"""
        if not self.selected_expense:
            show_warning("يرجى اختيار مصروف للتعديل")
            return
        
        try:
            dialog = ExpenseDialog(self.frame, "تعديل المصروف", self.db, self.selected_expense)
            if dialog.result:
                show_success("تم تحديث المصروف بنجاح")
                self.refresh_expenses()
        except Exception as e:
            show_error(f"خطأ في تعديل المصروف: {e}")
    
    def delete_expense(self):
        """حذف المصروف المحدد"""
        if not self.selected_expense:
            show_warning("يرجى اختيار مصروف للحذف")
            return
        
        if ask_confirmation(f"هل أنت متأكد من حذف المصروف '{self.selected_expense.description}'؟"):
            try:
                self.db.delete_expense(self.selected_expense.id)
                show_success("تم حذف المصروف بنجاح")
                self.refresh_expenses()
                self.selected_expense = None
            except Exception as e:
                show_error(f"خطأ في حذف المصروف: {e}")


class ExpenseDialog:
    """نافذة حوار إضافة/تعديل مصروف"""
    
    def __init__(self, parent, title, db, expense=None):
        self.result = None
        self.db = db
        self.expense = expense
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_dialog()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل البيانات إذا كان تعديل
        if self.expense:
            self.load_expense_data()
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 400) // 2
        y = (self.dialog.winfo_screenheight() - 350) // 2
        self.dialog.geometry(f"400x350+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # نوع المصروف
        ttk.Label(main_frame, text="نوع المصروف:").pack(anchor=tk.W, pady=(0, 5))
        self.type_var = tk.StringVar()
        type_combo = ttk.Combobox(main_frame, textvariable=self.type_var, state="readonly")
        type_combo['values'] = ["كهرباء", "مياه", "غاز", "إنترنت", "نظافة", "صيانة", "إيجار", "أخرى"]
        type_combo.pack(fill=tk.X, pady=(0, 10))
        
        # الوصف
        ttk.Label(main_frame, text="الوصف:").pack(anchor=tk.W, pady=(0, 5))
        self.description_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.description_var).pack(fill=tk.X, pady=(0, 10))
        
        # المبلغ
        ttk.Label(main_frame, text="المبلغ (جنيه):").pack(anchor=tk.W, pady=(0, 5))
        self.amount_var = tk.StringVar()
        ttk.Entry(main_frame, textvariable=self.amount_var).pack(fill=tk.X, pady=(0, 10))
        
        # التاريخ
        ttk.Label(main_frame, text="التاريخ:").pack(anchor=tk.W, pady=(0, 5))
        self.date_var = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        ttk.Entry(main_frame, textvariable=self.date_var).pack(fill=tk.X, pady=(0, 10))
        
        # الملاحظات
        ttk.Label(main_frame, text="ملاحظات:").pack(anchor=tk.W, pady=(0, 5))
        self.notes_text = tk.Text(main_frame, height=4)
        self.notes_text.pack(fill=tk.X, pady=(0, 20))
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=10)
        
        ttk.Button(buttons_frame, text="💾 حفظ", command=self.save_expense).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)
    
    def load_expense_data(self):
        """تحميل بيانات المصروف للتعديل"""
        if self.expense:
            self.type_var.set(self.expense.type)
            self.description_var.set(self.expense.description)
            self.amount_var.set(str(self.expense.amount))
            self.date_var.set(self.expense.date)
            self.notes_text.insert(1.0, self.expense.notes or "")
    
    def save_expense(self):
        """حفظ المصروف"""
        try:
            # التحقق من البيانات
            if not self.type_var.get():
                show_error("يرجى اختيار نوع المصروف")
                return
            
            if not self.description_var.get().strip():
                show_error("يرجى إدخال وصف المصروف")
                return
            
            amount = safe_float_convert(self.amount_var.get(), 0)
            if amount <= 0:
                show_error("يرجى إدخال مبلغ صحيح")
                return
            
            # حفظ المصروف
            if self.expense:
                # تعديل
                self.db.update_expense(
                    expense_id=self.expense.id,
                    type=self.type_var.get(),
                    description=self.description_var.get().strip(),
                    amount=amount,
                    date=self.date_var.get(),
                    notes=self.notes_text.get(1.0, tk.END).strip()
                )
            else:
                # إضافة جديد
                self.db.add_expense(
                    type=self.type_var.get(),
                    description=self.description_var.get().strip(),
                    amount=amount,
                    date=self.date_var.get(),
                    notes=self.notes_text.get(1.0, tk.END).strip()
                )
            
            self.result = True
            self.dialog.destroy()
            
        except Exception as e:
            show_error(f"خطأ في حفظ المصروف: {e}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
