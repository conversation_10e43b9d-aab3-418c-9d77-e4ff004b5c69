"""
واجهة إدارة الفواتير
Invoices Management Interface
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from utils.config import FONTS, INVOICE_SETTINGS
from utils.helpers import (
    show_success, show_error, show_warning, ask_confirmation,
    format_currency, format_datetime
)

class InvoicesFrame:
    """إطار إدارة الفواتير"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.create_interface()
        self.refresh_invoices()
    
    def create_interface(self):
        """إنشاء واجهة إدارة الفواتير"""
        # عنوان الصفحة
        title_label = ttk.Label(
            self.frame, 
            text="🧾 إدارة الفواتير", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار البحث والفلاتر
        self.create_search_section()
        
        # إطار قائمة الفواتير
        self.create_invoices_list()
        
        # إطار تفاصيل الفاتورة
        self.create_invoice_details()
    
    def create_search_section(self):
        """إنشاء قسم البحث والفلاتر"""
        search_frame = ttk.Frame(self.frame)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # فلتر التاريخ
        ttk.Label(search_frame, text="من تاريخ:").pack(side=tk.LEFT, padx=5)
        self.from_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.from_date_entry = ttk.Entry(search_frame, textvariable=self.from_date_var, width=12)
        self.from_date_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(search_frame, text="إلى تاريخ:").pack(side=tk.LEFT, padx=5)
        self.to_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.to_date_entry = ttk.Entry(search_frame, textvariable=self.to_date_var, width=12)
        self.to_date_entry.pack(side=tk.LEFT, padx=5)
        
        # البحث بالعميل
        ttk.Label(search_frame, text="العميل:").pack(side=tk.LEFT, padx=(20, 5))
        self.customer_search_var = tk.StringVar()
        self.customer_search_entry = ttk.Entry(search_frame, textvariable=self.customer_search_var, width=20)
        self.customer_search_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(search_frame)
        buttons_frame.pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="🔍 بحث", 
            command=self.search_invoices
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="➕ فاتورة جديدة", 
            command=self.create_new_invoice
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame,
            text="✏️ تعديل الفاتورة",
            command=self.edit_invoice
        ).pack(side=tk.LEFT, padx=2)

        ttk.Button(
            buttons_frame,
            text="💰 تعديل الدفعة",
            command=self.edit_invoice_payment
        ).pack(side=tk.LEFT, padx=2)

        ttk.Button(
            buttons_frame,
            text="🖨️ طباعة",
            command=self.print_invoice
        ).pack(side=tk.LEFT, padx=2)

        ttk.Button(
            buttons_frame,
            text="🔄 تحديث",
            command=self.refresh_invoices
        ).pack(side=tk.LEFT, padx=2)
    
    def create_invoices_list(self):
        """إنشاء قائمة الفواتير"""
        list_frame = ttk.LabelFrame(self.frame, text="قائمة الفواتير", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # جدول الفواتير
        columns = {
            'id': {'text': 'رقم الفاتورة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 120},
            'date': {'text': 'التاريخ', 'width': 100},
            'final_amount': {'text': 'المبلغ النهائي', 'width': 100},
            'paid_amount': {'text': 'المدفوع', 'width': 80},
            'remaining_amount': {'text': 'المتبقي', 'width': 80},
            'payment_status': {'text': 'حالة الدفع', 'width': 100},
            'payment_method': {'text': 'طريقة الدفع', 'width': 100}
        }
        
        self.invoices_tree = ttk.Treeview(list_frame)
        self.setup_treeview(self.invoices_tree, columns)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.invoices_tree.yview)
        self.invoices_tree.configure(yscrollcommand=scrollbar.set)
        
        self.invoices_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث التحديد
        self.invoices_tree.bind('<<TreeviewSelect>>', self.on_invoice_select)
    
    def create_invoice_details(self):
        """إنشاء قسم تفاصيل الفاتورة"""
        details_frame = ttk.LabelFrame(self.frame, text="تفاصيل الفاتورة", padding=10)
        details_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # معلومات الفاتورة
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X)
        
        # العمود الأول
        col1_frame = ttk.Frame(info_frame)
        col1_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col1_frame, text="رقم الفاتورة:", font=FONTS['default']).pack(anchor=tk.W)
        self.invoice_id_label = ttk.Label(col1_frame, text="-", font=FONTS['heading'])
        self.invoice_id_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col1_frame, text="العميل:", font=FONTS['default']).pack(anchor=tk.W)
        self.customer_label = ttk.Label(col1_frame, text="-")
        self.customer_label.pack(anchor=tk.W, pady=(0, 10))
        
        # العمود الثاني
        col2_frame = ttk.Frame(info_frame)
        col2_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col2_frame, text="التاريخ:", font=FONTS['default']).pack(anchor=tk.W)
        self.date_label = ttk.Label(col2_frame, text="-")
        self.date_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col2_frame, text="طريقة الدفع:", font=FONTS['default']).pack(anchor=tk.W)
        self.payment_method_label = ttk.Label(col2_frame, text="-")
        self.payment_method_label.pack(anchor=tk.W, pady=(0, 10))
        
        # العمود الثالث
        col3_frame = ttk.Frame(info_frame)
        col3_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col3_frame, text="المبلغ النهائي:", font=FONTS['default']).pack(anchor=tk.W)
        self.final_amount_label = ttk.Label(col3_frame, text="-", font=FONTS['heading'], foreground="green")
        self.final_amount_label.pack(anchor=tk.W, pady=(0, 10))
        
        # تفاصيل المبالغ
        amounts_frame = ttk.LabelFrame(details_frame, text="تفاصيل المبالغ", padding=5)
        amounts_frame.pack(fill=tk.X, pady=10)
        
        amounts_info_frame = ttk.Frame(amounts_frame)
        amounts_info_frame.pack(fill=tk.X)
        
        # مبلغ الجلسة
        ttk.Label(amounts_info_frame, text="مبلغ الجلسة:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.session_amount_label = ttk.Label(amounts_info_frame, text="-")
        self.session_amount_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # مبلغ المشتريات
        ttk.Label(amounts_info_frame, text="مبلغ المشتريات:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.purchases_amount_label = ttk.Label(amounts_info_frame, text="-")
        self.purchases_amount_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # الإجمالي قبل الخصم
        ttk.Label(amounts_info_frame, text="الإجمالي:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.total_amount_label = ttk.Label(amounts_info_frame, text="-")
        self.total_amount_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        # الخصم
        ttk.Label(amounts_info_frame, text="الخصم:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.discount_label = ttk.Label(amounts_info_frame, text="-", foreground="red")
        self.discount_label.grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)
        
        # المبلغ النهائي
        ttk.Label(amounts_info_frame, text="المبلغ النهائي:", font=FONTS['heading']).grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.final_amount_detail_label = ttk.Label(amounts_info_frame, text="-", font=FONTS['heading'], foreground="green")
        self.final_amount_detail_label.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # الملاحظات
        ttk.Label(details_frame, text="الملاحظات:", font=FONTS['default']).pack(anchor=tk.W, pady=(10, 0))
        self.notes_text = tk.Text(details_frame, height=2, state=tk.DISABLED)
        self.notes_text.pack(fill=tk.X, pady=5)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def refresh_invoices(self):
        """تحديث قائمة الفواتير"""
        try:
            # مسح البيانات الحالية
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)

            # الحصول على الفواتير من قاعدة البيانات
            invoices = self.db.get_invoices()

            for invoice in invoices:
                # تحديد لون حالة الدفع
                status_color = "green" if invoice.payment_status == "مدفوع" else "red" if invoice.payment_status == "غير مدفوع" else "orange"

                self.invoices_tree.insert('', 'end', values=(
                    invoice.id,
                    invoice.customer_name,
                    format_datetime(invoice.invoice_date, include_time=False),
                    format_currency(invoice.final_amount),
                    format_currency(invoice.paid_amount),
                    format_currency(invoice.remaining_amount),
                    invoice.payment_status,
                    invoice.payment_method
                ))

        except Exception as e:
            show_error(f"خطأ في تحديث قائمة الفواتير: {e}")
    
    def search_invoices(self):
        """البحث في الفواتير"""
        # هنا يمكن إضافة منطق البحث الفعلي
        self.refresh_invoices()
    
    def on_invoice_select(self, event=None):
        """عند اختيار فاتورة"""
        selection = self.invoices_tree.selection()
        if selection:
            item = self.invoices_tree.item(selection[0])
            values = item['values']
            
            # تحديث تفاصيل الفاتورة
            self.invoice_id_label.config(text=f"#{values[0]}")
            self.customer_label.config(text=values[1])
            self.date_label.config(text=values[2])
            self.payment_method_label.config(text=values[8])
            self.final_amount_label.config(text=f"{values[7]} ريال")
            
            # تحديث تفاصيل المبالغ
            self.session_amount_label.config(text=f"{values[3]} ريال")
            self.purchases_amount_label.config(text=f"{values[4]} ريال")
            self.total_amount_label.config(text=f"{values[5]} ريال")
            self.discount_label.config(text=f"{values[6]} ريال")
            self.final_amount_detail_label.config(text=f"{values[7]} ريال")
            
            # تحديث الملاحظات
            self.notes_text.config(state=tk.NORMAL)
            self.notes_text.delete(1.0, tk.END)
            self.notes_text.insert(1.0, "لا توجد ملاحظات")
            self.notes_text.config(state=tk.DISABLED)
    
    def create_new_invoice(self):
        """إنشاء فاتورة جديدة"""
        # الحصول على الجلسات المنتهية غير المفوترة
        try:
            # هنا يمكن إضافة منطق للحصول على الجلسات غير المفوترة
            dialog = InvoiceDialog(self.frame, "إنشاء فاتورة جديدة", self.db)
            if dialog.result:
                show_success("تم إنشاء الفاتورة بنجاح")
                self.refresh_invoices()
        except Exception as e:
            show_error(f"خطأ في إنشاء الفاتورة: {e}")

    def print_invoice(self):
        """طباعة الفاتورة المحددة"""
        selection = self.invoices_tree.selection()
        if not selection:
            show_warning("يرجى اختيار فاتورة للطباعة")
            return

        item = self.invoices_tree.item(selection[0])
        invoice_id = item['values'][0]

        try:
            # الحصول على بيانات الفاتورة
            invoices = self.db.get_invoices()
            invoice = next((inv for inv in invoices if inv.id == invoice_id), None)

            if not invoice:
                show_error("لم يتم العثور على الفاتورة")
                return

            # إنشاء نافذة معاينة الطباعة
            self.show_print_preview(invoice)

        except Exception as e:
            show_error(f"خطأ في طباعة الفاتورة: {e}")

    def show_print_preview(self, invoice):
        """عرض معاينة الطباعة"""
        # إنشاء نافذة معاينة
        preview_window = tk.Toplevel(self.frame)
        preview_window.title(f"معاينة الفاتورة #{invoice.id}")
        preview_window.geometry("600x800")
        preview_window.transient(self.frame)

        # توسيط النافذة
        x = (preview_window.winfo_screenwidth() - 600) // 2
        y = (preview_window.winfo_screenheight() - 800) // 2
        preview_window.geometry(f"600x800+{x}+{y}")

        # إطار المحتوى
        content_frame = ttk.Frame(preview_window, padding=20)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء محتوى الفاتورة
        invoice_content = self.generate_invoice_content(invoice)

        # عرض المحتوى
        text_widget = tk.Text(content_frame, wrap=tk.WORD, font=('Arial', 10))
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(1.0, invoice_content)
        text_widget.config(state=tk.DISABLED)

        # أزرار الطباعة
        buttons_frame = ttk.Frame(content_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        ttk.Button(
            buttons_frame,
            text="🖨️ طباعة",
            command=lambda: self.print_to_file(invoice, invoice_content)
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            buttons_frame,
            text="❌ إغلاق",
            command=preview_window.destroy
        ).pack(side=tk.LEFT, padx=5)

    def generate_invoice_content(self, invoice):
        """إنشاء محتوى الفاتورة"""
        from utils.config import INVOICE_SETTINGS

        content = f"""
{'='*60}
                    مساحة العمل
                 فاتورة رقم: {invoice.id}
{'='*60}

التاريخ: {format_datetime(invoice.invoice_date)}
العميل: {invoice.customer_name}

{'='*60}
                    تفاصيل الفاتورة
{'='*60}

مبلغ الجلسة:                    {format_currency(invoice.session_amount)}
مبلغ المشتريات:                 {format_currency(invoice.purchases_amount)}
                                    _______________
الإجمالي:                       {format_currency(invoice.total_amount)}

الخصم ({invoice.discount_type}):              {format_currency(invoice.discount)}
                                    _______________
المبلغ النهائي:                  {format_currency(invoice.final_amount)}

المبلغ المدفوع:                  {format_currency(invoice.paid_amount)}
المبلغ المتبقي:                  {format_currency(invoice.remaining_amount)}

طريقة الدفع: {invoice.payment_method}
حالة الدفع: {invoice.payment_status}

{'='*60}

الملاحظات:
{invoice.notes if invoice.notes else 'لا توجد ملاحظات'}

{'='*60}
                شكراً لتعاملكم معنا
                  {INVOICE_SETTINGS['company_name']}
                  {INVOICE_SETTINGS['company_phone']}
{'='*60}
        """
        return content.strip()

    def print_to_file(self, invoice, content):
        """حفظ الفاتورة كملف نصي"""
        try:
            import os
            from tkinter import filedialog

            # اختيار مكان الحفظ
            filename = f"فاتورة_{invoice.id}_{invoice.customer_name}.txt"
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialvalue=filename
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                show_success(f"تم حفظ الفاتورة في:\n{file_path}")

                # فتح الملف
                if ask_confirmation("هل تريد فتح الملف الآن؟"):
                    os.startfile(file_path)

        except Exception as e:
            show_error(f"خطأ في حفظ الفاتورة: {e}")

    def edit_invoice(self):
        """تعديل الفاتورة المحددة"""
        selection = self.invoices_tree.selection()
        if not selection:
            show_warning("يرجى اختيار فاتورة للتعديل")
            return

        item = self.invoices_tree.item(selection[0])
        invoice_id = item['values'][0]

        try:
            # الحصول على بيانات الفاتورة
            invoices = self.db.get_invoices()
            invoice = next((inv for inv in invoices if inv.id == invoice_id), None)

            if not invoice:
                show_error("لم يتم العثور على الفاتورة")
                return

            # فتح نافذة تعديل الفاتورة
            dialog = InvoiceEditDialog(self.frame, "تعديل الفاتورة", self.db, invoice)
            if dialog.result:
                show_success("تم تحديث الفاتورة بنجاح")
                self.refresh_invoices()
        except Exception as e:
            show_error(f"خطأ في تعديل الفاتورة: {e}")

    def edit_invoice_payment(self):
        """تعديل دفعة الفاتورة"""
        selection = self.invoices_tree.selection()
        if not selection:
            show_warning("يرجى اختيار فاتورة لتعديل دفعتها")
            return

        item = self.invoices_tree.item(selection[0])
        invoice_id = item['values'][0]

        try:
            dialog = PaymentDialog(self.frame, "تعديل الدفعة", self.db, invoice_id)
            if dialog.result:
                show_success("تم تحديث الدفعة بنجاح")
                self.refresh_invoices()
        except Exception as e:
            show_error(f"خطأ في تحديث الدفعة: {e}")


class InvoiceDialog:
    """نافذة حوار إنشاء فاتورة"""

    def __init__(self, parent, title, db, session_id=None):
        self.result = None
        self.db = db
        self.session_id = session_id

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_dialog()

        # إنشاء الواجهة
        self.create_interface()

    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 500) // 2
        y = (self.dialog.winfo_screenheight() - 400) // 2
        self.dialog.geometry(f"500x400+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # اختيار الجلسة
        ttk.Label(main_frame, text="اختر الجلسة:").pack(anchor=tk.W, pady=5)
        self.session_var = tk.StringVar()
        self.session_combo = ttk.Combobox(main_frame, textvariable=self.session_var, state="readonly", width=50)
        self.session_combo.pack(fill=tk.X, pady=5)

        # تحميل الجلسات المنتهية
        self.load_completed_sessions()

        # ربط حدث تغيير الجلسة
        self.session_combo.bind('<<ComboboxSelected>>', self.on_session_select)

        # قسم تفاصيل المبالغ
        amounts_frame = ttk.LabelFrame(main_frame, text="تفاصيل المبالغ", padding=10)
        amounts_frame.pack(fill=tk.X, pady=10)

        # مبلغ الجلسة
        session_amount_frame = ttk.Frame(amounts_frame)
        session_amount_frame.pack(fill=tk.X, pady=2)
        ttk.Label(session_amount_frame, text="مبلغ الجلسة:").pack(side=tk.LEFT)
        self.session_amount_label = ttk.Label(session_amount_frame, text="0.00 جنيه", font=('Arial', 10, 'bold'))
        self.session_amount_label.pack(side=tk.RIGHT)

        # مبلغ المشتريات
        purchases_amount_frame = ttk.Frame(amounts_frame)
        purchases_amount_frame.pack(fill=tk.X, pady=2)
        ttk.Label(purchases_amount_frame, text="مبلغ المشتريات:").pack(side=tk.LEFT)
        self.purchases_amount_label = ttk.Label(purchases_amount_frame, text="0.00 جنيه", font=('Arial', 10, 'bold'))
        self.purchases_amount_label.pack(side=tk.RIGHT)

        # الإجمالي قبل الخصم
        total_before_frame = ttk.Frame(amounts_frame)
        total_before_frame.pack(fill=tk.X, pady=2)
        ttk.Label(total_before_frame, text="الإجمالي قبل الخصم:").pack(side=tk.LEFT)
        self.total_before_label = ttk.Label(total_before_frame, text="0.00 جنيه", font=('Arial', 10, 'bold'), foreground="blue")
        self.total_before_label.pack(side=tk.RIGHT)

        # الخصم
        discount_frame = ttk.Frame(main_frame)
        discount_frame.pack(fill=tk.X, pady=10)

        ttk.Label(discount_frame, text="الخصم:").pack(side=tk.LEFT)
        self.discount_var = tk.StringVar(value="0")
        self.discount_entry = ttk.Entry(discount_frame, textvariable=self.discount_var, width=10)
        self.discount_entry.pack(side=tk.LEFT, padx=5)

        self.discount_type_var = tk.StringVar(value="مبلغ")
        self.discount_type_combo = ttk.Combobox(discount_frame, textvariable=self.discount_type_var,
                                              values=["مبلغ", "نسبة"], state="readonly", width=10)
        self.discount_type_combo.pack(side=tk.LEFT, padx=5)

        # المبلغ المدفوع
        payment_frame = ttk.Frame(main_frame)
        payment_frame.pack(fill=tk.X, pady=10)

        ttk.Label(payment_frame, text="المبلغ المدفوع:").pack(side=tk.LEFT)
        self.paid_amount_var = tk.StringVar(value="0")
        self.paid_amount_entry = ttk.Entry(payment_frame, textvariable=self.paid_amount_var, width=15)
        self.paid_amount_entry.pack(side=tk.LEFT, padx=5)

        # طريقة الدفع
        ttk.Label(payment_frame, text="طريقة الدفع:").pack(side=tk.LEFT, padx=(20, 5))
        self.payment_method_var = tk.StringVar(value="نقدي")
        self.payment_method_combo = ttk.Combobox(payment_frame, textvariable=self.payment_method_var,
                                               values=["نقدي", "بطاقة", "تحويل", "شيك"], state="readonly", width=10)
        self.payment_method_combo.pack(side=tk.LEFT, padx=5)

        # الملاحظات
        ttk.Label(main_frame, text="الملاحظات:").pack(anchor=tk.W, pady=(10, 0))
        self.notes_text = tk.Text(main_frame, height=4)
        self.notes_text.pack(fill=tk.X, pady=5)

        # إضافة حقل تعديل المبلغ النهائي
        final_amount_frame = ttk.Frame(amounts_frame)
        final_amount_frame.pack(fill=tk.X, pady=5)
        ttk.Label(final_amount_frame, text="المبلغ النهائي المطلوب:").pack(side=tk.LEFT)
        self.final_amount_var = tk.StringVar()
        self.final_amount_entry = ttk.Entry(final_amount_frame, textvariable=self.final_amount_var, width=15)
        self.final_amount_entry.pack(side=tk.RIGHT, padx=5)
        self.final_amount_entry.bind('<KeyRelease>', self.on_final_amount_change)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="💾 حفظ الفاتورة", command=self.create_invoice).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)

    def load_completed_sessions(self):
        """تحميل الجلسات المنتهية"""
        try:
            if self.session_id:
                # إذا تم تحديد جلسة معينة
                sessions = self.db.get_completed_sessions()
                target_session = next((s for s in sessions if s.id == self.session_id), None)
                if target_session:
                    option = f"جلسة #{target_session.id} - {target_session.customer_name} - {format_currency(target_session.total_amount)}"
                    self.session_combo['values'] = [option]
                    self.session_combo.set(option)
                    self.on_session_select()  # تحديث التفاصيل
                else:
                    self.session_combo['values'] = ["الجلسة المحددة غير موجودة"]
                    self.session_combo.set("الجلسة المحددة غير موجودة")
            else:
                # تحميل جميع الجلسات المنتهية (بما في ذلك المفوترة)
                all_sessions = self.db.get_all_sessions()
                completed_sessions = [s for s in all_sessions if not s.is_active]

                if completed_sessions:
                    session_options = []
                    for session in completed_sessions:
                        # إضافة معلومة إذا كانت الجلسة مفوترة
                        invoices = self.db.get_invoices()
                        is_invoiced = any(inv.session_id == session.id for inv in invoices)
                        status = " (مفوترة)" if is_invoiced else ""

                        option = f"#{session.id} - {session.customer_name} - {format_currency(session.total_amount)}{status}"
                        session_options.append(option)

                    self.session_combo['values'] = session_options
                    if session_options:
                        self.session_combo.set(session_options[0])
                        self.on_session_select()  # تحديث التفاصيل
                else:
                    self.session_combo['values'] = ["لا توجد جلسات منتهية"]
                    self.session_combo.set("لا توجد جلسات منتهية")
        except Exception as e:
            print(f"خطأ في تحميل الجلسات: {e}")
            self.session_combo['values'] = ["خطأ في تحميل الجلسات"]
            self.session_combo.set("خطأ في تحميل الجلسات")

    def on_session_select(self, event=None):
        """عند اختيار جلسة - تحديث تفاصيل المبالغ"""
        try:
            session_text = self.session_var.get()
            if not session_text or session_text in ["لا توجد جلسات منتهية", "خطأ في تحميل الجلسات", "الجلسة المحددة غير موجودة"]:
                return

            # استخراج معرف الجلسة
            session_id = int(session_text.split("#")[1].split(" ")[0])

            # الحصول على بيانات الجلسة
            session = self.db.get_session(session_id)
            if not session:
                return

            # الحصول على المشتريات
            purchases = self.db.get_session_purchases(session_id)
            purchases_amount = sum(p.total_price for p in purchases)

            # تحديث التسميات
            self.session_amount_label.config(text=f"{session.total_amount:.2f} جنيه")
            self.purchases_amount_label.config(text=f"{purchases_amount:.2f} جنيه")

            total_before_discount = session.total_amount + purchases_amount
            self.total_before_label.config(text=f"{total_before_discount:.2f} جنيه")

            # تحديث المبلغ النهائي المطلوب (افتراضياً نفس الإجمالي)
            self.final_amount_var.set(str(total_before_discount))

            print(f"تفاصيل الجلسة #{session_id}:")
            print(f"  مبلغ الجلسة: {session.total_amount:.2f} جنيه")
            print(f"  مبلغ المشتريات: {purchases_amount:.2f} جنيه")
            print(f"  الإجمالي: {total_before_discount:.2f} جنيه")

        except Exception as e:
            print(f"خطأ في تحديث تفاصيل الجلسة: {e}")

    def on_final_amount_change(self, event=None):
        """عند تغيير المبلغ النهائي المطلوب"""
        try:
            from utils.helpers import safe_float_convert
            final_amount = safe_float_convert(self.final_amount_var.get())
            print(f"تم تعديل المبلغ النهائي إلى: {final_amount:.2f} جنيه")
        except:
            pass

    def create_invoice(self):
        """إنشاء الفاتورة"""
        try:
            from utils.helpers import safe_float_convert

            session_text = self.session_var.get()
            if not session_text or session_text in ["لا توجد جلسات منتهية", "خطأ في تحميل الجلسات"]:
                show_error("يرجى اختيار جلسة صحيحة")
                return

            # استخراج معرف الجلسة من النص
            try:
                session_id = int(session_text.split("#")[1].split(" ")[0])
            except:
                show_error("خطأ في تحديد الجلسة")
                return

            discount = safe_float_convert(self.discount_var.get())
            paid_amount = safe_float_convert(self.paid_amount_var.get())
            custom_final_amount = safe_float_convert(self.final_amount_var.get())

            # إنشاء الفاتورة مع المبلغ المخصص
            invoice_id = self.db.create_invoice_with_custom_amount(
                session_id=session_id,
                custom_final_amount=custom_final_amount,
                discount=discount,
                discount_type=self.discount_type_var.get(),
                paid_amount=paid_amount,
                payment_method=self.payment_method_var.get(),
                notes=self.notes_text.get(1.0, tk.END).strip()
            )

            self.result = invoice_id
            show_success("تم حفظ الفاتورة بنجاح!")
            self.dialog.destroy()

        except Exception as e:
            show_error(f"خطأ في إنشاء الفاتورة: {e}")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()


class PaymentDialog:
    """نافذة حوار تعديل الدفعة"""

    def __init__(self, parent, title, db, invoice_id):
        self.result = None
        self.db = db
        self.invoice_id = invoice_id

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_dialog()

        # إنشاء الواجهة
        self.create_interface()

    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 400) // 2
        y = (self.dialog.winfo_screenheight() - 300) // 2
        self.dialog.geometry(f"400x300+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات الفاتورة
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الفاتورة", padding=10)
        info_frame.pack(fill=tk.X, pady=10)

        # هنا يمكن عرض معلومات الفاتورة الحالية
        ttk.Label(info_frame, text=f"رقم الفاتورة: {self.invoice_id}").pack(anchor=tk.W)

        # المبلغ المدفوع الجديد
        payment_frame = ttk.Frame(main_frame)
        payment_frame.pack(fill=tk.X, pady=10)

        ttk.Label(payment_frame, text="المبلغ المدفوع الجديد:").pack(anchor=tk.W)
        self.paid_amount_var = tk.StringVar()
        self.paid_amount_entry = ttk.Entry(payment_frame, textvariable=self.paid_amount_var, width=20)
        self.paid_amount_entry.pack(fill=tk.X, pady=5)

        # طريقة الدفع
        ttk.Label(payment_frame, text="طريقة الدفع:").pack(anchor=tk.W, pady=(10, 0))
        self.payment_method_var = tk.StringVar(value="نقدي")
        self.payment_method_combo = ttk.Combobox(payment_frame, textvariable=self.payment_method_var,
                                               values=["نقدي", "بطاقة", "تحويل", "شيك"], state="readonly")
        self.payment_method_combo.pack(fill=tk.X, pady=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="💾 تحديث الدفعة", command=self.update_payment).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)

    def update_payment(self):
        """تحديث الدفعة"""
        try:
            from utils.helpers import safe_float_convert

            paid_amount = safe_float_convert(self.paid_amount_var.get())
            if paid_amount < 0:
                show_error("المبلغ المدفوع لا يمكن أن يكون سالباً")
                return

            self.db.update_invoice_payment(
                self.invoice_id,
                paid_amount,
                self.payment_method_var.get()
            )

            self.result = True
            self.dialog.destroy()

        except Exception as e:
            show_error(f"خطأ في تحديث الدفعة: {e}")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()


class InvoiceEditDialog:
    """نافذة تعديل الفاتورة"""

    def __init__(self, parent, title, db, invoice):
        self.result = None
        self.db = db
        self.invoice = invoice

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_dialog()

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_invoice_data()

    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 600) // 2
        y = (self.dialog.winfo_screenheight() - 500) // 2
        self.dialog.geometry(f"600x500+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان
        title_label = ttk.Label(
            main_frame,
            text=f"تعديل الفاتورة #{self.invoice.id}",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))

        # معلومات الجلسة (للقراءة فقط)
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الجلسة", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(info_frame, text=f"العميل: {self.invoice.customer_name}").pack(anchor=tk.W, pady=2)
        ttk.Label(info_frame, text=f"رقم الجلسة: {self.invoice.session_id}").pack(anchor=tk.W, pady=2)

        # المبالغ القابلة للتعديل
        amounts_frame = ttk.LabelFrame(main_frame, text="المبالغ", padding=10)
        amounts_frame.pack(fill=tk.X, pady=(0, 10))

        # مبلغ الجلسة
        session_frame = ttk.Frame(amounts_frame)
        session_frame.pack(fill=tk.X, pady=2)
        ttk.Label(session_frame, text="مبلغ الجلسة:").pack(side=tk.LEFT)
        self.session_amount_var = tk.StringVar(value=str(self.invoice.session_amount))
        ttk.Entry(session_frame, textvariable=self.session_amount_var, width=15).pack(side=tk.RIGHT)

        # مبلغ المشتريات
        purchases_frame = ttk.Frame(amounts_frame)
        purchases_frame.pack(fill=tk.X, pady=2)
        ttk.Label(purchases_frame, text="مبلغ المشتريات:").pack(side=tk.LEFT)
        self.purchases_amount_var = tk.StringVar(value=str(self.invoice.purchases_amount))
        ttk.Entry(purchases_frame, textvariable=self.purchases_amount_var, width=15).pack(side=tk.RIGHT)

        # الخصم
        discount_frame = ttk.Frame(amounts_frame)
        discount_frame.pack(fill=tk.X, pady=2)
        ttk.Label(discount_frame, text="الخصم:").pack(side=tk.LEFT)
        self.discount_var = tk.StringVar(value=str(self.invoice.discount))
        ttk.Entry(discount_frame, textvariable=self.discount_var, width=10).pack(side=tk.RIGHT, padx=5)

        self.discount_type_var = tk.StringVar(value=self.invoice.discount_type)
        ttk.Combobox(discount_frame, textvariable=self.discount_type_var,
                    values=["مبلغ", "نسبة"], state="readonly", width=8).pack(side=tk.RIGHT)

        # المبلغ المدفوع
        paid_frame = ttk.Frame(amounts_frame)
        paid_frame.pack(fill=tk.X, pady=2)
        ttk.Label(paid_frame, text="المبلغ المدفوع:").pack(side=tk.LEFT)
        self.paid_amount_var = tk.StringVar(value=str(self.invoice.paid_amount))
        ttk.Entry(paid_frame, textvariable=self.paid_amount_var, width=15).pack(side=tk.RIGHT)

        # طريقة الدفع
        payment_frame = ttk.Frame(amounts_frame)
        payment_frame.pack(fill=tk.X, pady=2)
        ttk.Label(payment_frame, text="طريقة الدفع:").pack(side=tk.LEFT)
        self.payment_method_var = tk.StringVar(value=self.invoice.payment_method)
        ttk.Combobox(payment_frame, textvariable=self.payment_method_var,
                    values=["نقدي", "بطاقة", "تحويل", "شيك"], state="readonly", width=12).pack(side=tk.RIGHT)

        # الملاحظات
        notes_frame = ttk.LabelFrame(main_frame, text="الملاحظات", padding=10)
        notes_frame.pack(fill=tk.X, pady=(0, 10))

        self.notes_text = tk.Text(notes_frame, height=4)
        self.notes_text.pack(fill=tk.X)
        self.notes_text.insert(1.0, self.invoice.notes or "")

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="💾 حفظ التغييرات", command=self.save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)

    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        pass  # البيانات محملة بالفعل في create_interface

    def save_changes(self):
        """حفظ التغييرات"""
        try:
            from utils.helpers import safe_float_convert

            # التحقق من البيانات
            session_amount = safe_float_convert(self.session_amount_var.get())
            purchases_amount = safe_float_convert(self.purchases_amount_var.get())
            discount = safe_float_convert(self.discount_var.get())
            paid_amount = safe_float_convert(self.paid_amount_var.get())

            if session_amount < 0 or purchases_amount < 0 or discount < 0 or paid_amount < 0:
                show_error("لا يمكن أن تكون المبالغ سالبة")
                return

            # تحديث الفاتورة
            self.db.update_invoice(
                invoice_id=self.invoice.id,
                session_amount=session_amount,
                purchases_amount=purchases_amount,
                discount=discount,
                discount_type=self.discount_type_var.get(),
                paid_amount=paid_amount,
                payment_method=self.payment_method_var.get(),
                notes=self.notes_text.get(1.0, tk.END).strip()
            )

            self.result = True
            self.dialog.destroy()

        except Exception as e:
            show_error(f"خطأ في حفظ التغييرات: {e}")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
