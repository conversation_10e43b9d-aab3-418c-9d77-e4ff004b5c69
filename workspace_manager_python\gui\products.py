"""
واجهة إدارة المنتجات
Products Management Interface
"""

import tkinter as tk
from tkinter import ttk
from database.models import Product
from utils.config import FONTS
from utils.helpers import (
    show_success, show_error, show_warning, ask_confirmation,
    format_currency, safe_float_convert, safe_int_convert, is_valid_positive_number
)

class ProductsFrame:
    """إطار إدارة المنتجات"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.selected_product = None
        self.create_interface()
        self.refresh_products()
    
    def create_interface(self):
        """إنشاء واجهة إدارة المنتجات"""
        # عنوان الصفحة
        title_label = ttk.Label(
            self.frame, 
            text="🛍️ إدارة المنتجات والمشروبات", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار البحث والأزرار
        self.create_search_section()
        
        # إطار قائمة المنتجات
        self.create_products_list()
        
        # إطار تفاصيل المنتج
        self.create_product_details()
    
    def create_search_section(self):
        """إنشاء قسم البحث والأزرار"""
        search_frame = ttk.Frame(self.frame)
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # حقل البحث
        ttk.Label(search_frame, text="🔍 البحث:").pack(side=tk.LEFT, padx=5)
        
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=5)
        self.search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # فلتر الفئة
        ttk.Label(search_frame, text="الفئة:").pack(side=tk.LEFT, padx=(20, 5))
        self.category_var = tk.StringVar()
        self.category_combo = ttk.Combobox(search_frame, textvariable=self.category_var, width=15, state="readonly")
        self.category_combo.pack(side=tk.LEFT, padx=5)
        self.category_combo.bind('<<ComboboxSelected>>', self.on_search_change)
        
        # أزرار الإجراءات
        buttons_frame = ttk.Frame(search_frame)
        buttons_frame.pack(side=tk.RIGHT, padx=5)
        
        ttk.Button(
            buttons_frame, 
            text="➕ إضافة منتج", 
            command=self.add_product
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="✏️ تعديل", 
            command=self.edit_product
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🗑️ حذف", 
            command=self.delete_product
        ).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(
            buttons_frame, 
            text="🔄 تحديث", 
            command=self.refresh_products
        ).pack(side=tk.LEFT, padx=2)
    
    def create_products_list(self):
        """إنشاء قائمة المنتجات"""
        list_frame = ttk.LabelFrame(self.frame, text="قائمة المنتجات", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # جدول المنتجات
        columns = {
            'id': {'text': 'الرقم', 'width': 60},
            'name': {'text': 'اسم المنتج', 'width': 150},
            'category': {'text': 'الفئة', 'width': 100},
            'price': {'text': 'السعر', 'width': 80},
            'cost': {'text': 'التكلفة', 'width': 80},
            'profit': {'text': 'الربح', 'width': 80},
            'quantity': {'text': 'الكمية', 'width': 80},
            'status': {'text': 'الحالة', 'width': 80}
        }
        
        self.products_tree = ttk.Treeview(list_frame)
        self.setup_treeview(self.products_tree, columns)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط حدث التحديد
        self.products_tree.bind('<<TreeviewSelect>>', self.on_product_select)
        self.products_tree.bind('<Double-1>', self.edit_product)
    
    def create_product_details(self):
        """إنشاء قسم تفاصيل المنتج"""
        details_frame = ttk.LabelFrame(self.frame, text="تفاصيل المنتج", padding=10)
        details_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # معلومات المنتج
        info_frame = ttk.Frame(details_frame)
        info_frame.pack(fill=tk.X)
        
        # العمود الأول
        col1_frame = ttk.Frame(info_frame)
        col1_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col1_frame, text="اسم المنتج:", font=FONTS['default']).pack(anchor=tk.W)
        self.name_label = ttk.Label(col1_frame, text="-", font=FONTS['heading'])
        self.name_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col1_frame, text="الفئة:", font=FONTS['default']).pack(anchor=tk.W)
        self.category_label = ttk.Label(col1_frame, text="-")
        self.category_label.pack(anchor=tk.W, pady=(0, 10))
        
        # العمود الثاني
        col2_frame = ttk.Frame(info_frame)
        col2_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col2_frame, text="السعر:", font=FONTS['default']).pack(anchor=tk.W)
        self.price_label = ttk.Label(col2_frame, text="-")
        self.price_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col2_frame, text="التكلفة:", font=FONTS['default']).pack(anchor=tk.W)
        self.cost_label = ttk.Label(col2_frame, text="-")
        self.cost_label.pack(anchor=tk.W, pady=(0, 10))
        
        # العمود الثالث
        col3_frame = ttk.Frame(info_frame)
        col3_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        ttk.Label(col3_frame, text="الربح:", font=FONTS['default']).pack(anchor=tk.W)
        self.profit_label = ttk.Label(col3_frame, text="-", foreground="green")
        self.profit_label.pack(anchor=tk.W, pady=(0, 10))
        
        ttk.Label(col3_frame, text="الكمية المتاحة:", font=FONTS['default']).pack(anchor=tk.W)
        self.quantity_label = ttk.Label(col3_frame, text="-")
        self.quantity_label.pack(anchor=tk.W, pady=(0, 10))
        
        # الوصف
        ttk.Label(details_frame, text="الوصف:", font=FONTS['default']).pack(anchor=tk.W, pady=(10, 0))
        self.description_text = tk.Text(details_frame, height=2, state=tk.DISABLED)
        self.description_text.pack(fill=tk.X, pady=5)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def refresh_products(self):
        """تحديث قائمة المنتجات"""
        try:
            # مسح البيانات الحالية
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)
            
            # الحصول على المنتجات
            search_term = self.search_var.get()
            products = self.db.get_products(search_term)
            
            # تحديث قائمة الفئات
            categories = list(set(p.category for p in products if p.category))
            categories.insert(0, "جميع الفئات")
            self.category_combo['values'] = categories
            if not self.category_var.get():
                self.category_var.set("جميع الفئات")
            
            # فلترة حسب الفئة
            selected_category = self.category_var.get()
            if selected_category and selected_category != "جميع الفئات":
                products = [p for p in products if p.category == selected_category]
            
            # إضافة المنتجات للجدول
            for product in products:
                status = "متاح" if product.is_active else "غير متاح"
                profit = product.profit
                profit_color = "green" if profit > 0 else "red" if profit < 0 else "black"
                
                self.products_tree.insert('', 'end', values=(
                    product.id,
                    product.name,
                    product.category,
                    format_currency(product.price),
                    format_currency(product.cost),
                    format_currency(profit),
                    product.quantity,
                    status
                ))
                
        except Exception as e:
            show_error(f"خطأ في تحديث قائمة المنتجات: {e}")
    
    def on_search_change(self, event=None):
        """عند تغيير نص البحث أو الفئة"""
        self.refresh_products()
    
    def on_product_select(self, event=None):
        """عند اختيار منتج"""
        selection = self.products_tree.selection()
        if selection:
            item = self.products_tree.item(selection[0])
            product_id = item['values'][0]
            self.load_product_details(product_id)
    
    def load_product_details(self, product_id):
        """تحميل تفاصيل المنتج"""
        try:
            products = self.db.get_products()
            product = next((p for p in products if p.id == product_id), None)
            
            if product:
                self.selected_product = product
                self.name_label.config(text=product.name)
                self.category_label.config(text=product.category or "-")
                self.price_label.config(text=format_currency(product.price))
                self.cost_label.config(text=format_currency(product.cost))
                
                profit = product.profit
                profit_color = "green" if profit > 0 else "red" if profit < 0 else "black"
                self.profit_label.config(text=format_currency(profit), foreground=profit_color)
                
                self.quantity_label.config(text=str(product.quantity))
                
                # تحديث الوصف
                self.description_text.config(state=tk.NORMAL)
                self.description_text.delete(1.0, tk.END)
                self.description_text.insert(1.0, product.description or "لا يوجد وصف")
                self.description_text.config(state=tk.DISABLED)
            else:
                self.clear_product_details()
                
        except Exception as e:
            show_error(f"خطأ في تحميل تفاصيل المنتج: {e}")
    
    def clear_product_details(self):
        """مسح تفاصيل المنتج"""
        self.selected_product = None
        self.name_label.config(text="-")
        self.category_label.config(text="-")
        self.price_label.config(text="-")
        self.cost_label.config(text="-")
        self.profit_label.config(text="-", foreground="black")
        self.quantity_label.config(text="-")
        
        self.description_text.config(state=tk.NORMAL)
        self.description_text.delete(1.0, tk.END)
        self.description_text.config(state=tk.DISABLED)
    
    def add_product(self):
        """إضافة منتج جديد"""
        dialog = ProductDialog(self.frame, "إضافة منتج جديد")
        if dialog.result:
            try:
                product_id = self.db.add_product(dialog.result)
                show_success("تم إضافة المنتج بنجاح")
                self.refresh_products()
            except Exception as e:
                show_error(f"خطأ في إضافة المنتج: {e}")
    
    def edit_product(self):
        """تعديل المنتج المحدد"""
        if not self.selected_product:
            show_warning("يرجى اختيار منتج للتعديل")
            return
        
        dialog = ProductDialog(self.frame, "تعديل بيانات المنتج", self.selected_product)
        if dialog.result:
            try:
                self.db.update_product(dialog.result)
                show_success("تم تحديث بيانات المنتج بنجاح")
                self.refresh_products()
                self.load_product_details(dialog.result.id)
            except Exception as e:
                show_error(f"خطأ في تحديث المنتج: {e}")
    
    def delete_product(self):
        """حذف المنتج المحدد"""
        if not self.selected_product:
            show_warning("يرجى اختيار منتج للحذف")
            return
        
        if ask_confirmation(f"هل أنت متأكد من حذف المنتج '{self.selected_product.name}'؟"):
            try:
                self.db.delete_product(self.selected_product.id)
                show_success("تم حذف المنتج بنجاح")
                self.refresh_products()
                self.clear_product_details()
            except Exception as e:
                show_error(f"خطأ في حذف المنتج: {e}")


class ProductDialog:
    """نافذة حوار إضافة/تعديل المنتج"""
    
    def __init__(self, parent, title, product=None):
        self.result = None
        self.product = product
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_dialog()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تعبئة البيانات إذا كان تعديل
        if product:
            self.populate_fields()
        
        # التركيز على حقل الاسم
        self.name_entry.focus()
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 450) // 2
        y = (self.dialog.winfo_screenheight() - 400) // 2
        self.dialog.geometry(f"450x400+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # حقول الإدخال
        row = 0
        
        ttk.Label(main_frame, text="اسم المنتج *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.name_entry = ttk.Entry(main_frame, width=30)
        self.name_entry.grid(row=row, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        row += 1
        
        ttk.Label(main_frame, text="الفئة:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.category_entry = ttk.Entry(main_frame, width=30)
        self.category_entry.grid(row=row, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        row += 1
        
        ttk.Label(main_frame, text="السعر *:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.price_entry = ttk.Entry(main_frame, width=30)
        self.price_entry.grid(row=row, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        row += 1
        
        ttk.Label(main_frame, text="التكلفة:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.cost_entry = ttk.Entry(main_frame, width=30)
        self.cost_entry.grid(row=row, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        row += 1
        
        ttk.Label(main_frame, text="الكمية:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.quantity_entry = ttk.Entry(main_frame, width=30)
        self.quantity_entry.grid(row=row, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        row += 1
        
        ttk.Label(main_frame, text="الباركود:").grid(row=row, column=0, sticky=tk.W, pady=5)
        self.barcode_entry = ttk.Entry(main_frame, width=30)
        self.barcode_entry.grid(row=row, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        row += 1
        
        ttk.Label(main_frame, text="الوصف:").grid(row=row, column=0, sticky=tk.NW, pady=5)
        self.description_text = tk.Text(main_frame, width=30, height=4)
        self.description_text.grid(row=row, column=1, sticky=tk.EW, pady=5, padx=(10, 0))
        row += 1
        
        # حالة المنتج
        self.active_var = tk.BooleanVar(value=True)
        self.active_check = ttk.Checkbutton(main_frame, text="منتج متاح", variable=self.active_var)
        self.active_check.grid(row=row, column=1, sticky=tk.W, pady=10, padx=(10, 0))
        row += 1
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=row, column=0, columnspan=2, pady=20)
        
        ttk.Button(buttons_frame, text="💾 حفظ", command=self.save_product).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)
        
        # تكوين الأعمدة
        main_frame.columnconfigure(1, weight=1)
    
    def populate_fields(self):
        """تعبئة الحقول بالبيانات الحالية"""
        if self.product:
            self.name_entry.insert(0, self.product.name)
            self.category_entry.insert(0, self.product.category or "")
            self.price_entry.insert(0, str(self.product.price))
            self.cost_entry.insert(0, str(self.product.cost))
            self.quantity_entry.insert(0, str(self.product.quantity))
            self.barcode_entry.insert(0, self.product.barcode or "")
            self.description_text.insert(1.0, self.product.description or "")
            self.active_var.set(self.product.is_active)
    
    def save_product(self):
        """حفظ بيانات المنتج"""
        # التحقق من صحة البيانات
        name = self.name_entry.get().strip()
        if not name:
            show_error("يرجى إدخال اسم المنتج")
            return
        
        price_str = self.price_entry.get().strip()
        if not price_str or not is_valid_positive_number(price_str):
            show_error("يرجى إدخال سعر صحيح")
            return
        
        cost_str = self.cost_entry.get().strip()
        if cost_str and not is_valid_positive_number(cost_str):
            show_error("يرجى إدخال تكلفة صحيحة")
            return
        
        quantity_str = self.quantity_entry.get().strip()
        if quantity_str and not quantity_str.isdigit():
            show_error("يرجى إدخال كمية صحيحة")
            return
        
        # إنشاء كائن المنتج
        product = Product(
            id=self.product.id if self.product else None,
            name=name,
            category=self.category_entry.get().strip(),
            price=safe_float_convert(price_str),
            cost=safe_float_convert(cost_str),
            quantity=safe_int_convert(quantity_str),
            barcode=self.barcode_entry.get().strip(),
            description=self.description_text.get(1.0, tk.END).strip(),
            is_active=self.active_var.get()
        )
        
        self.result = product
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
