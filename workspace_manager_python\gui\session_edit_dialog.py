"""
نافذة تعديل الجلسة مع إمكانية تعديل الأوقات والأسعار
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from utils.config import FONTS
from utils.helpers import (
    show_success, show_error, show_warning,
    safe_float_convert, safe_int_convert
)


class SessionEditDialog:
    """نافذة حوار تعديل الجلسة"""
    
    def __init__(self, parent, title, db, session):
        self.result = None
        self.db = db
        self.session = session
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_dialog()
        
        # إنشاء الواجهة
        self.create_interface()
        
        # تحميل بيانات الجلسة
        self.load_session_data()
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 500) // 2
        y = (self.dialog.winfo_screenheight() - 600) // 2
        self.dialog.geometry(f"500x600+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # معلومات الجلسة
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الجلسة", padding=10)
        info_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(info_frame, text=f"رقم الجلسة: {self.session.id}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"العميل: {self.session.customer_name}").pack(anchor=tk.W)
        
        # تعديل الأوقات
        time_frame = ttk.LabelFrame(main_frame, text="تعديل الأوقات", padding=10)
        time_frame.pack(fill=tk.X, pady=10)
        
        # وقت البداية
        start_frame = ttk.Frame(time_frame)
        start_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(start_frame, text="وقت البداية:").pack(side=tk.LEFT)
        
        # تاريخ البداية
        self.start_date_var = tk.StringVar()
        ttk.Entry(start_frame, textvariable=self.start_date_var, width=12).pack(side=tk.LEFT, padx=5)
        ttk.Label(start_frame, text="(YYYY-MM-DD)").pack(side=tk.LEFT)
        
        # وقت البداية
        self.start_time_var = tk.StringVar()
        ttk.Entry(start_frame, textvariable=self.start_time_var, width=8).pack(side=tk.LEFT, padx=5)
        ttk.Label(start_frame, text="(HH:MM)").pack(side=tk.LEFT)
        
        # وقت النهاية
        end_frame = ttk.Frame(time_frame)
        end_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(end_frame, text="وقت النهاية:").pack(side=tk.LEFT)
        
        # تاريخ النهاية
        self.end_date_var = tk.StringVar()
        ttk.Entry(end_frame, textvariable=self.end_date_var, width=12).pack(side=tk.LEFT, padx=5)
        ttk.Label(end_frame, text="(YYYY-MM-DD)").pack(side=tk.LEFT)
        
        # وقت النهاية
        self.end_time_var = tk.StringVar()
        ttk.Entry(end_frame, textvariable=self.end_time_var, width=8).pack(side=tk.LEFT, padx=5)
        ttk.Label(end_frame, text="(HH:MM)").pack(side=tk.LEFT)
        
        # أزرار سريعة للوقت
        quick_time_frame = ttk.Frame(time_frame)
        quick_time_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(quick_time_frame, text="الآن", command=self.set_end_time_now).pack(side=tk.LEFT, padx=2)
        ttk.Button(quick_time_frame, text="حساب تلقائي", command=self.calculate_duration).pack(side=tk.LEFT, padx=2)
        
        # نوع التسعير
        pricing_frame = ttk.LabelFrame(main_frame, text="نوع التسعير", padding=10)
        pricing_frame.pack(fill=tk.X, pady=10)
        
        self.pricing_type_var = tk.StringVar()
        
        # تسعير بالساعة
        hourly_frame = ttk.Frame(pricing_frame)
        hourly_frame.pack(fill=tk.X, pady=5)
        
        ttk.Radiobutton(
            hourly_frame, 
            text="تسعير بالساعة", 
            variable=self.pricing_type_var, 
            value="hourly",
            command=self.on_pricing_change
        ).pack(side=tk.LEFT)
        
        ttk.Label(hourly_frame, text="السعر:").pack(side=tk.LEFT, padx=(20, 5))
        self.hourly_rate_var = tk.StringVar()
        self.hourly_entry = ttk.Entry(hourly_frame, textvariable=self.hourly_rate_var, width=10)
        self.hourly_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(hourly_frame, text="جنيه/ساعة").pack(side=tk.LEFT)
        
        # تسعير باليوم
        daily_frame = ttk.Frame(pricing_frame)
        daily_frame.pack(fill=tk.X, pady=5)
        
        ttk.Radiobutton(
            daily_frame, 
            text="تسعير باليوم (24 ساعة)", 
            variable=self.pricing_type_var, 
            value="daily",
            command=self.on_pricing_change
        ).pack(side=tk.LEFT)
        
        ttk.Label(daily_frame, text="السعر:").pack(side=tk.LEFT, padx=(20, 5))
        self.daily_rate_var = tk.StringVar()
        self.daily_entry = ttk.Entry(daily_frame, textvariable=self.daily_rate_var, width=10)
        self.daily_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(daily_frame, text="جنيه/يوم").pack(side=tk.LEFT)
        
        # معلومات المدة والتكلفة
        calc_frame = ttk.LabelFrame(main_frame, text="حساب المدة والتكلفة", padding=10)
        calc_frame.pack(fill=tk.X, pady=10)
        
        # المدة
        duration_frame = ttk.Frame(calc_frame)
        duration_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(duration_frame, text="المدة:", font=FONTS['bold']).pack(side=tk.LEFT)
        self.duration_label = ttk.Label(duration_frame, text="--", foreground="blue")
        self.duration_label.pack(side=tk.LEFT, padx=10)
        
        # التكلفة
        cost_frame = ttk.Frame(calc_frame)
        cost_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(cost_frame, text="التكلفة:", font=FONTS['bold']).pack(side=tk.LEFT)
        self.cost_label = ttk.Label(cost_frame, text="--", foreground="green")
        self.cost_label.pack(side=tk.LEFT, padx=10)
        
        # الملاحظات
        notes_frame = ttk.LabelFrame(main_frame, text="ملاحظات", padding=10)
        notes_frame.pack(fill=tk.X, pady=10)
        
        self.notes_text = tk.Text(notes_frame, height=4)
        self.notes_text.pack(fill=tk.X)
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)
        
        ttk.Button(buttons_frame, text="💾 حفظ التغييرات", command=self.save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="🔄 إعادة حساب", command=self.calculate_duration).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)
    
    def load_session_data(self):
        """تحميل بيانات الجلسة"""
        # تحميل الأوقات
        if self.session.start_time:
            start_dt = self.session.start_time
            self.start_date_var.set(start_dt.strftime('%Y-%m-%d'))
            self.start_time_var.set(start_dt.strftime('%H:%M'))
        
        if self.session.end_time:
            end_dt = self.session.end_time
            self.end_date_var.set(end_dt.strftime('%Y-%m-%d'))
            self.end_time_var.set(end_dt.strftime('%H:%M'))
        else:
            # إذا لم تكن منتهية، استخدم الوقت الحالي
            now = datetime.now()
            self.end_date_var.set(now.strftime('%Y-%m-%d'))
            self.end_time_var.set(now.strftime('%H:%M'))
        
        # تحميل التسعير
        pricing_type = getattr(self.session, 'pricing_type', 'hourly')
        self.pricing_type_var.set(pricing_type)
        
        self.hourly_rate_var.set(str(getattr(self.session, 'hourly_rate', 0)))
        self.daily_rate_var.set(str(getattr(self.session, 'daily_rate', 0)))
        
        # تحميل الملاحظات
        if self.session.notes:
            self.notes_text.insert(1.0, self.session.notes)
        
        # تحديث الحالة
        self.on_pricing_change()
        self.calculate_duration()
    
    def on_pricing_change(self):
        """عند تغيير نوع التسعير"""
        pricing_type = self.pricing_type_var.get()
        if pricing_type == "hourly":
            self.hourly_entry.config(state='normal')
            self.daily_entry.config(state='disabled')
        else:
            self.hourly_entry.config(state='disabled')
            self.daily_entry.config(state='normal')
        
        self.calculate_duration()
    
    def set_end_time_now(self):
        """تعيين وقت النهاية للوقت الحالي"""
        now = datetime.now()
        self.end_date_var.set(now.strftime('%Y-%m-%d'))
        self.end_time_var.set(now.strftime('%H:%M'))
        self.calculate_duration()
    
    def calculate_duration(self):
        """حساب المدة والتكلفة"""
        try:
            # تحليل الأوقات
            start_date_str = self.start_date_var.get()
            start_time_str = self.start_time_var.get()
            end_date_str = self.end_date_var.get()
            end_time_str = self.end_time_var.get()
            
            start_datetime = datetime.strptime(f"{start_date_str} {start_time_str}", '%Y-%m-%d %H:%M')
            end_datetime = datetime.strptime(f"{end_date_str} {end_time_str}", '%Y-%m-%d %H:%M')
            
            if end_datetime <= start_datetime:
                self.duration_label.config(text="خطأ: وقت النهاية يجب أن يكون بعد وقت البداية")
                self.cost_label.config(text="--")
                return
            
            # حساب المدة
            duration = end_datetime - start_datetime
            total_seconds = duration.total_seconds()
            total_minutes = int(total_seconds / 60)
            total_hours = total_seconds / 3600
            
            # تنسيق المدة
            hours = total_minutes // 60
            minutes = total_minutes % 60
            duration_text = f"{hours} ساعة و {minutes} دقيقة ({total_minutes} دقيقة)"
            
            # حساب التكلفة
            pricing_type = self.pricing_type_var.get()
            if pricing_type == "daily":
                daily_rate = safe_float_convert(self.daily_rate_var.get(), 0)
                days = total_hours / 24
                total_cost = days * daily_rate
                cost_text = f"{total_cost:.2f} جنيه ({days:.2f} يوم × {daily_rate} جنيه)"
            else:
                hourly_rate = safe_float_convert(self.hourly_rate_var.get(), 0)
                total_cost = total_hours * hourly_rate
                cost_text = f"{total_cost:.2f} جنيه ({total_hours:.2f} ساعة × {hourly_rate} جنيه)"
            
            # تحديث الواجهة
            self.duration_label.config(text=duration_text)
            self.cost_label.config(text=cost_text)
            
        except ValueError as e:
            self.duration_label.config(text="خطأ في تنسيق التاريخ/الوقت")
            self.cost_label.config(text="--")
        except Exception as e:
            self.duration_label.config(text=f"خطأ: {e}")
            self.cost_label.config(text="--")
    
    def save_changes(self):
        """حفظ التغييرات"""
        try:
            # التحقق من البيانات
            start_date_str = self.start_date_var.get()
            start_time_str = self.start_time_var.get()
            end_date_str = self.end_date_var.get()
            end_time_str = self.end_time_var.get()
            
            start_datetime = datetime.strptime(f"{start_date_str} {start_time_str}", '%Y-%m-%d %H:%M')
            end_datetime = datetime.strptime(f"{end_date_str} {end_time_str}", '%Y-%m-%d %H:%M')
            
            if end_datetime <= start_datetime:
                show_error("وقت النهاية يجب أن يكون بعد وقت البداية")
                return
            
            # التحقق من الأسعار
            pricing_type = self.pricing_type_var.get()
            if pricing_type == "hourly":
                hourly_rate = safe_float_convert(self.hourly_rate_var.get(), 0)
                if hourly_rate <= 0:
                    show_error("يرجى إدخال سعر ساعي صحيح")
                    return
                daily_rate = 0
            else:
                daily_rate = safe_float_convert(self.daily_rate_var.get(), 0)
                if daily_rate <= 0:
                    show_error("يرجى إدخال سعر يومي صحيح")
                    return
                hourly_rate = 0
            
            # حفظ التغييرات في قاعدة البيانات
            notes = self.notes_text.get(1.0, tk.END).strip()
            
            self.db.update_session_details(
                session_id=self.session.id,
                start_time=start_datetime,
                end_time=end_datetime,
                hourly_rate=hourly_rate,
                daily_rate=daily_rate,
                pricing_type=pricing_type,
                notes=notes
            )
            
            self.result = True
            self.dialog.destroy()
            
        except ValueError:
            show_error("تنسيق التاريخ أو الوقت غير صحيح")
        except Exception as e:
            show_error(f"خطأ في حفظ التغييرات: {e}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()
