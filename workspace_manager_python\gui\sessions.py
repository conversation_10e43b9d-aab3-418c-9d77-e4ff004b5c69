"""
واجهة إدارة الجلسات
Sessions Management Interface
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from database.models import Session
from utils.config import FONTS, DEFAULT_HOURLY_RATE
from utils.helpers import (
    show_success, show_error, show_warning, ask_confirmation,
    format_currency, format_datetime, calculate_time_difference
)

class SessionsFrame:
    """إطار إدارة الجلسات"""
    
    def __init__(self, parent, db):
        self.parent = parent
        self.db = db
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        self.create_interface()
        self.refresh_sessions()
        self.update_timer()
    
    def create_interface(self):
        """إنشاء واجهة إدارة الجلسات"""
        # عنوان الصفحة
        title_label = ttk.Label(
            self.frame, 
            text="⏰ إدارة الجلسات", 
            font=FONTS['title']
        )
        title_label.pack(pady=10)
        
        # إطار بدء جلسة جديدة
        self.create_new_session_section()
        
        # إطار الجلسات النشطة
        self.create_active_sessions_section()
        
        # إطار تاريخ الجلسات
        self.create_sessions_history_section()
    
    def create_new_session_section(self):
        """إنشاء قسم بدء جلسة جديدة"""
        new_session_frame = ttk.LabelFrame(self.frame, text="🆕 بدء جلسة جديدة", padding=10)
        new_session_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # حقول بدء الجلسة
        fields_frame = ttk.Frame(new_session_frame)
        fields_frame.pack(fill=tk.X)
        
        # اختيار العميل
        ttk.Label(fields_frame, text="العميل:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.customer_var = tk.StringVar()
        self.customer_combo = ttk.Combobox(fields_frame, textvariable=self.customer_var, width=25, state="readonly")
        self.customer_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        # سعر الساعة
        ttk.Label(fields_frame, text="سعر الساعة:").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        self.hourly_rate_var = tk.StringVar(value=str(DEFAULT_HOURLY_RATE))
        self.hourly_rate_entry = ttk.Entry(fields_frame, textvariable=self.hourly_rate_var, width=10)
        self.hourly_rate_entry.grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # زر بدء الجلسة
        ttk.Button(
            fields_frame, 
            text="▶️ بدء الجلسة", 
            command=self.start_new_session
        ).grid(row=0, column=4, padx=20, pady=5)
        
        # تحديث قائمة العملاء
        self.refresh_customers_list()
    
    def create_active_sessions_section(self):
        """إنشاء قسم الجلسات النشطة"""
        active_frame = ttk.LabelFrame(self.frame, text="🔴 الجلسات النشطة", padding=10)
        active_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # جدول الجلسات النشطة
        columns = {
            'id': {'text': 'رقم الجلسة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 150},
            'start_time': {'text': 'وقت البداية', 'width': 120},
            'duration': {'text': 'المدة', 'width': 100},
            'rate': {'text': 'سعر الساعة', 'width': 80},
            'current_cost': {'text': 'التكلفة الحالية', 'width': 100}
        }
        
        self.active_sessions_tree = ttk.Treeview(active_frame)
        self.setup_treeview(self.active_sessions_tree, columns)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(active_frame, orient=tk.VERTICAL, command=self.active_sessions_tree.yview)
        self.active_sessions_tree.configure(yscrollcommand=scrollbar.set)
        
        self.active_sessions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار الإجراءات
        actions_frame = ttk.Frame(active_frame)
        actions_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(
            actions_frame,
            text="⏹️ إنهاء الجلسة",
            command=self.end_session
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            actions_frame,
            text="✏️ تعديل الجلسة",
            command=self.edit_session
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            actions_frame,
            text="👁️ عرض التفاصيل",
            command=self.view_session_details
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            actions_frame,
            text="🗑️ حذف الجلسة",
            command=self.delete_session
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            actions_frame,
            text="🛍️ إضافة مشترى",
            command=self.add_purchase
        ).pack(side=tk.LEFT, padx=5)

        ttk.Button(
            actions_frame,
            text="🧾 إنشاء فاتورة",
            command=self.create_invoice
        ).pack(side=tk.LEFT, padx=5)
    
    def create_sessions_history_section(self):
        """إنشاء قسم تاريخ الجلسات"""
        history_frame = ttk.LabelFrame(self.frame, text="📋 تاريخ الجلسات", padding=10)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # فلتر التاريخ
        filter_frame = ttk.Frame(history_frame)
        filter_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(filter_frame, text="من تاريخ:").pack(side=tk.LEFT, padx=5)
        self.from_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.from_date_entry = ttk.Entry(filter_frame, textvariable=self.from_date_var, width=12)
        self.from_date_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(filter_frame, text="إلى تاريخ:").pack(side=tk.LEFT, padx=5)
        self.to_date_var = tk.StringVar(value=datetime.now().strftime("%Y-%m-%d"))
        self.to_date_entry = ttk.Entry(filter_frame, textvariable=self.to_date_var, width=12)
        self.to_date_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(
            filter_frame, 
            text="🔍 بحث", 
            command=self.search_sessions
        ).pack(side=tk.LEFT, padx=10)
        
        # جدول تاريخ الجلسات
        history_columns = {
            'id': {'text': 'رقم الجلسة', 'width': 80},
            'customer': {'text': 'العميل', 'width': 150},
            'start_time': {'text': 'وقت البداية', 'width': 120},
            'end_time': {'text': 'وقت النهاية', 'width': 120},
            'duration': {'text': 'المدة', 'width': 100},
            'total_amount': {'text': 'إجمالي التكلفة', 'width': 100}
        }
        
        self.history_tree = ttk.Treeview(history_frame)
        self.setup_treeview(self.history_tree, history_columns)
        
        # شريط التمرير للتاريخ
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_treeview(self, treeview, columns):
        """إعداد جدول البيانات"""
        treeview['columns'] = list(columns.keys())
        treeview['show'] = 'headings'
        
        for col_id, col_info in columns.items():
            treeview.heading(col_id, text=col_info['text'])
            treeview.column(col_id, width=col_info['width'], anchor='center')
    
    def refresh_customers_list(self):
        """تحديث قائمة العملاء"""
        try:
            customers = self.db.get_customers()
            customer_names = [f"{c.name} (ID: {c.id})" for c in customers if c.is_active]
            self.customer_combo['values'] = customer_names
        except Exception as e:
            show_error(f"خطأ في تحميل قائمة العملاء: {e}")
    
    def start_new_session(self):
        """بدء جلسة جديدة"""
        try:
            # التحقق من البيانات
            customer_text = self.customer_var.get()
            if not customer_text:
                show_warning("يرجى اختيار عميل")
                return
            
            # استخراج معرف العميل
            customer_id = int(customer_text.split("ID: ")[1].split(")")[0])
            customer_name = customer_text.split(" (ID:")[0]
            
            hourly_rate = float(self.hourly_rate_var.get())
            if hourly_rate <= 0:
                show_warning("يرجى إدخال سعر ساعة صحيح")
                return
            
            # التحقق من عدم وجود جلسة نشطة للعميل
            active_sessions = self.db.get_active_sessions()
            if any(s.customer_id == customer_id for s in active_sessions):
                show_warning("يوجد جلسة نشطة لهذا العميل بالفعل")
                return
            
            # بدء الجلسة
            session_id = self.db.start_session(customer_id, customer_name, hourly_rate)
            show_success(f"تم بدء الجلسة رقم {session_id} بنجاح")
            
            # تحديث الواجهة
            self.refresh_sessions()
            
        except ValueError:
            show_error("يرجى إدخال سعر ساعة صحيح")
        except Exception as e:
            show_error(f"خطأ في بدء الجلسة: {e}")
    
    def refresh_sessions(self):
        """تحديث الجلسات"""
        self.refresh_active_sessions()
        self.search_sessions()
    
    def refresh_active_sessions(self):
        """تحديث الجلسات النشطة"""
        try:
            # مسح البيانات الحالية
            for item in self.active_sessions_tree.get_children():
                self.active_sessions_tree.delete(item)
            
            # الحصول على الجلسات النشطة
            active_sessions = self.db.get_active_sessions()
            
            for session in active_sessions:
                # حساب المدة الحالية
                time_diff = calculate_time_difference(session.start_time)
                duration_str = time_diff['formatted']
                
                # حساب التكلفة الحالية (الوقت + المشتريات)
                current_hours = time_diff['total_minutes'] / 60
                session_cost = current_hours * session.hourly_rate

                # إضافة تكلفة المشتريات
                purchases = self.db.get_session_purchases(session.id)
                purchases_cost = sum(p.total_price for p in purchases)

                total_current_cost = session_cost + purchases_cost

                self.active_sessions_tree.insert('', 'end', values=(
                    session.id,
                    session.customer_name,
                    format_datetime(session.start_time),
                    duration_str,
                    format_currency(session.hourly_rate),
                    format_currency(total_current_cost)
                ))
                
        except Exception as e:
            show_error(f"خطأ في تحديث الجلسات النشطة: {e}")
    
    def search_sessions(self):
        """البحث في تاريخ الجلسات"""
        try:
            # مسح البيانات الحالية
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

            # الحصول على الجلسات المنتهية
            completed_sessions = self.db.get_completed_sessions()

            if completed_sessions:
                for session in completed_sessions:
                    # حساب المدة
                    if session.end_time:
                        time_diff = calculate_time_difference(session.start_time, session.end_time)
                        duration_str = time_diff['formatted']
                    else:
                        duration_str = "غير محدد"

                    # حساب الإجمالي مع المشتريات
                    purchases = self.db.get_session_purchases(session.id)
                    purchases_cost = sum(p.total_price for p in purchases)
                    total_with_purchases = session.total_amount + purchases_cost

                    self.history_tree.insert('', 'end', values=(
                        session.id,
                        session.customer_name,
                        format_datetime(session.start_time),
                        format_datetime(session.end_time) if session.end_time else "غير محدد",
                        duration_str,
                        format_currency(total_with_purchases)
                    ))
            else:
                self.history_tree.insert('', 'end', values=(
                    "-", "لا توجد جلسات منتهية", "-", "-", "-", "-"
                ))

        except Exception as e:
            show_error(f"خطأ في البحث: {e}")
    
    def end_session(self):
        """إنهاء الجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            show_warning("يرجى اختيار جلسة لإنهائها")
            return
        
        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]
        
        try:
            if ask_confirmation("هل أنت متأكد من إنهاء هذه الجلسة؟"):
                session = self.db.end_session(session_id)
                if session:
                    show_success(f"تم إنهاء الجلسة بنجاح\nالمدة: {session.total_hours:.2f} ساعة\nالتكلفة: {format_currency(session.total_amount)}")
                    self.refresh_sessions()
                else:
                    show_error("لم يتم العثور على الجلسة")
        except Exception as e:
            show_error(f"خطأ في إنهاء الجلسة: {e}")
    
    def add_purchase(self):
        """إضافة مشترى للجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            show_warning("يرجى اختيار جلسة لإضافة مشترى إليها")
            return

        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]

        try:
            dialog = PurchaseDialog(self.frame, "إضافة مشترى", self.db, session_id)
            if dialog.result:
                show_success("تم إضافة المشترى بنجاح")
                self.refresh_sessions()
        except Exception as e:
            show_error(f"خطأ في إضافة المشترى: {e}")
    
    def create_invoice(self):
        """إنشاء فاتورة للجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            show_warning("يرجى اختيار جلسة لإنشاء فاتورة لها")
            return

        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]
        customer_name = item['values'][1]

        # التأكد من إنهاء الجلسة أولاً
        if ask_confirmation(f"سيتم إنهاء الجلسة للعميل '{customer_name}' وإنشاء فاتورة.\nهل تريد المتابعة؟"):
            try:
                # إنهاء الجلسة
                session = self.db.end_session(session_id)
                if session:
                    # فتح نافذة إنشاء الفاتورة
                    from gui.invoices import InvoiceDialog
                    dialog = InvoiceDialog(self.frame, "إنشاء فاتورة", self.db, session_id)
                    if dialog.result:
                        show_success("تم إنشاء الفاتورة بنجاح")
                        self.refresh_sessions()
                else:
                    show_error("فشل في إنهاء الجلسة")
            except Exception as e:
                show_error(f"خطأ في إنشاء الفاتورة: {e}")
    
    def update_timer(self):
        """تحديث المؤقت كل دقيقة"""
        self.refresh_active_sessions()
        self.frame.after(60000, self.update_timer)  # تحديث كل دقيقة

    def edit_session(self):
        """تعديل الجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            show_warning("يرجى اختيار جلسة للتعديل")
            return

        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]

        try:
            dialog = SessionEditDialog(self.frame, "تعديل الجلسة", self.db, session_id)
            if dialog.result:
                show_success("تم تحديث الجلسة بنجاح")
                self.refresh_sessions()
        except Exception as e:
            show_error(f"خطأ في تعديل الجلسة: {e}")

    def view_session_details(self):
        """عرض تفاصيل الجلسة المحددة"""
        # التحقق من الجلسة النشطة أولاً
        selection = self.active_sessions_tree.selection()
        if selection:
            item = self.active_sessions_tree.item(selection[0])
            session_id = item['values'][0]
            self._show_session_details(session_id, is_active=True)
            return

        # التحقق من جلسات التاريخ
        selection = self.history_tree.selection()
        if selection:
            item = self.history_tree.item(selection[0])
            session_id = item['values'][0]
            self._show_session_details(session_id, is_active=False)
            return

        show_warning("يرجى اختيار جلسة لعرض تفاصيلها")

    def _show_session_details(self, session_id, is_active=False):
        """عرض نافذة تفاصيل الجلسة"""
        try:
            dialog = SessionDetailsDialog(self.frame, session_id, self.db, is_active)
        except Exception as e:
            show_error(f"خطأ في عرض تفاصيل الجلسة: {e}")

    def delete_session(self):
        """حذف الجلسة المحددة"""
        selection = self.active_sessions_tree.selection()
        if not selection:
            show_warning("يرجى اختيار جلسة للحذف")
            return

        item = self.active_sessions_tree.item(selection[0])
        session_id = item['values'][0]
        customer_name = item['values'][1]

        if ask_confirmation(f"هل أنت متأكد من حذف الجلسة للعميل '{customer_name}'؟\nسيتم حذف جميع المشتريات المرتبطة بها أيضاً."):
            try:
                self.db.delete_session(session_id)
                show_success("تم حذف الجلسة بنجاح")
                self.refresh_sessions()
            except Exception as e:
                show_error(f"خطأ في حذف الجلسة: {e}")


class SessionEditDialog:
    """نافذة حوار تعديل الجلسة"""

    def __init__(self, parent, title, db, session_id):
        self.result = None
        self.db = db
        self.session_id = session_id

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_dialog()

        # إنشاء الواجهة
        self.create_interface()

        # تحميل بيانات الجلسة
        self.load_session_data()

    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 400) // 2
        y = (self.dialog.winfo_screenheight() - 300) // 2
        self.dialog.geometry(f"400x300+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات الجلسة
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الجلسة", padding=10)
        info_frame.pack(fill=tk.X, pady=5)

        self.session_info_label = ttk.Label(info_frame, text=f"رقم الجلسة: {self.session_id}")
        self.session_info_label.pack(anchor=tk.W)

        # اسم العميل
        ttk.Label(main_frame, text="اسم العميل:").pack(anchor=tk.W, pady=(10, 0))
        self.customer_name_var = tk.StringVar()
        self.customer_name_entry = ttk.Entry(main_frame, textvariable=self.customer_name_var)
        self.customer_name_entry.pack(fill=tk.X, pady=5)

        # سعر الساعة
        ttk.Label(main_frame, text="سعر الساعة:").pack(anchor=tk.W, pady=(10, 0))
        self.hourly_rate_var = tk.StringVar()
        self.hourly_rate_entry = ttk.Entry(main_frame, textvariable=self.hourly_rate_var)
        self.hourly_rate_entry.pack(fill=tk.X, pady=5)

        # الملاحظات
        ttk.Label(main_frame, text="الملاحظات:").pack(anchor=tk.W, pady=(10, 0))
        self.notes_text = tk.Text(main_frame, height=4)
        self.notes_text.pack(fill=tk.X, pady=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        ttk.Button(buttons_frame, text="💾 حفظ التغييرات", command=self.save_changes).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=self.cancel).pack(side=tk.LEFT, padx=5)

    def load_session_data(self):
        """تحميل بيانات الجلسة الحالية"""
        try:
            # الحصول على بيانات الجلسة
            active_sessions = self.db.get_active_sessions()
            session = next((s for s in active_sessions if s.id == self.session_id), None)

            if session:
                self.customer_name_var.set(session.customer_name)
                self.hourly_rate_var.set(str(session.hourly_rate))
                self.notes_text.insert(1.0, session.notes or "")
            else:
                show_error("لم يتم العثور على الجلسة")
                self.dialog.destroy()

        except Exception as e:
            show_error(f"خطأ في تحميل بيانات الجلسة: {e}")
            self.dialog.destroy()

    def save_changes(self):
        """حفظ التغييرات"""
        try:
            from utils.helpers import safe_float_convert

            customer_name = self.customer_name_var.get().strip()
            if not customer_name:
                show_error("يرجى إدخال اسم العميل")
                return

            hourly_rate_str = self.hourly_rate_var.get().strip()
            if not hourly_rate_str:
                show_error("يرجى إدخال سعر الساعة")
                return

            hourly_rate = safe_float_convert(hourly_rate_str)
            if hourly_rate <= 0:
                show_error("يرجى إدخال سعر ساعة صحيح")
                return

            notes = self.notes_text.get(1.0, tk.END).strip()

            # تحديث الجلسة
            self.db.update_session(
                session_id=self.session_id,
                customer_name=customer_name,
                hourly_rate=hourly_rate,
                notes=notes
            )

            self.result = True
            self.dialog.destroy()

        except Exception as e:
            show_error(f"خطأ في حفظ التغييرات: {e}")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()


class PurchaseDialog:
    """نافذة حوار إضافة مشترى"""

    def __init__(self, parent, title, db, session_id):
        self.result = None
        self.db = db
        self.session_id = session_id

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x450")  # زيادة الارتفاع
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_dialog()

        # إنشاء الواجهة
        self.create_interface()

        # تحميل المنتجات
        self.load_products()

    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 450) // 2
        y = (self.dialog.winfo_screenheight() - 450) // 2
        self.dialog.geometry(f"450x450+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # معلومات الجلسة
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الجلسة", padding=10)
        info_frame.pack(fill=tk.X, pady=5)

        ttk.Label(info_frame, text=f"رقم الجلسة: {self.session_id}").pack(anchor=tk.W)

        # اختيار المنتج
        ttk.Label(main_frame, text="المنتج:").pack(anchor=tk.W, pady=(10, 0))
        self.product_var = tk.StringVar()
        self.product_combo = ttk.Combobox(main_frame, textvariable=self.product_var, state="readonly")
        self.product_combo.pack(fill=tk.X, pady=5)
        self.product_combo.bind('<<ComboboxSelected>>', self.on_product_select)

        # الكمية
        quantity_frame = ttk.Frame(main_frame)
        quantity_frame.pack(fill=tk.X, pady=10)

        ttk.Label(quantity_frame, text="الكمية:").pack(side=tk.LEFT)
        self.quantity_var = tk.StringVar(value="1")
        self.quantity_entry = ttk.Entry(quantity_frame, textvariable=self.quantity_var, width=10)
        self.quantity_entry.pack(side=tk.LEFT, padx=5)
        self.quantity_entry.bind('<KeyRelease>', self.calculate_total)

        # السعر
        ttk.Label(quantity_frame, text="السعر:").pack(side=tk.LEFT, padx=(20, 5))
        self.price_var = tk.StringVar()
        self.price_entry = ttk.Entry(quantity_frame, textvariable=self.price_var, width=10)
        self.price_entry.pack(side=tk.LEFT, padx=5)
        self.price_entry.bind('<KeyRelease>', self.calculate_total)

        # زر استعادة السعر الأصلي
        self.reset_price_button = ttk.Button(
            quantity_frame,
            text="↻",
            width=3,
            command=self.reset_price
        )
        self.reset_price_button.pack(side=tk.LEFT, padx=2)

        # الإجمالي
        total_frame = ttk.Frame(main_frame)
        total_frame.pack(fill=tk.X, pady=10)

        ttk.Label(total_frame, text="الإجمالي:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.total_label = ttk.Label(total_frame, text="0.00 جنيه", font=('Arial', 10, 'bold'), foreground="green")
        self.total_label.pack(side=tk.LEFT, padx=10)

        # الملاحظات
        ttk.Label(main_frame, text="الملاحظات:").pack(anchor=tk.W, pady=(10, 0))
        self.notes_text = tk.Text(main_frame, height=3)
        self.notes_text.pack(fill=tk.X, pady=5)

        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20, fill=tk.X)

        # توسيط الأزرار
        button_container = ttk.Frame(buttons_frame)
        button_container.pack(expand=True)

        add_button = ttk.Button(
            button_container,
            text="💾 إضافة المشترى",
            command=self.add_purchase,
            width=15
        )
        add_button.pack(side=tk.LEFT, padx=10, pady=5)

        cancel_button = ttk.Button(
            button_container,
            text="❌ إلغاء",
            command=self.cancel,
            width=12
        )
        cancel_button.pack(side=tk.LEFT, padx=10, pady=5)

    def load_products(self):
        """تحميل المنتجات"""
        try:
            print("بدء تحميل المنتجات...")  # رسالة تشخيصية
            products = self.db.get_products()
            print(f"تم العثور على {len(products)} منتج")  # رسالة تشخيصية

            product_options = []
            self.products_dict = {}

            for product in products:
                print(f"المنتج: {product.name}, نشط: {product.is_active}, الكمية: {product.quantity}")  # رسالة تشخيصية

                if product.is_active and product.quantity > 0:
                    option = f"{product.name} - {product.price:.2f} جنيه (متاح: {product.quantity})"
                    product_options.append(option)
                    self.products_dict[option] = product
                    print(f"تم إضافة المنتج: {option}")  # رسالة تشخيصية

            print(f"إجمالي المنتجات المتاحة: {len(product_options)}")  # رسالة تشخيصية

            if product_options:
                self.product_combo['values'] = product_options
                self.product_combo.set(product_options[0])
                self.on_product_select()
                print("تم تحديد المنتج الأول")
            else:
                # إضافة رسالة إذا لم توجد منتجات
                self.product_combo['values'] = ["لا توجد منتجات متاحة"]
                self.product_combo.set("لا توجد منتجات متاحة")
                print("لا توجد منتجات متاحة")

        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {e}")
            import traceback
            traceback.print_exc()
            # إضافة رسالة خطأ
            self.product_combo['values'] = ["خطأ في تحميل المنتجات"]
            self.product_combo.set("خطأ في تحميل المنتجات")

    def on_product_select(self, event=None):
        """عند اختيار منتج"""
        selected = self.product_var.get()
        if selected in self.products_dict:
            product = self.products_dict[selected]
            self.price_var.set(str(product.price))
            self.calculate_total()

    def reset_price(self):
        """استعادة السعر الأصلي للمنتج"""
        selected = self.product_var.get()
        if selected in self.products_dict:
            product = self.products_dict[selected]
            self.price_var.set(str(product.price))
            self.calculate_total()

    def calculate_total(self, event=None):
        """حساب الإجمالي"""
        try:
            from utils.helpers import safe_float_convert, safe_int_convert
            quantity = safe_int_convert(self.quantity_var.get(), 1)
            price = safe_float_convert(self.price_var.get(), 0)
            total = quantity * price
            self.total_label.config(text=f"{total:.2f} جنيه")
        except:
            self.total_label.config(text="0.00 جنيه")

    def add_purchase(self):
        """إضافة المشترى"""
        try:
            from utils.helpers import safe_float_convert, safe_int_convert

            selected = self.product_var.get()
            if not selected or selected not in self.products_dict:
                show_error("يرجى اختيار منتج")
                return

            product = self.products_dict[selected]
            quantity = safe_int_convert(self.quantity_var.get(), 1)
            price = safe_float_convert(self.price_var.get(), 0)

            if quantity <= 0:
                show_error("يرجى إدخال كمية صحيحة")
                return

            if quantity > product.quantity:
                show_error(f"الكمية المطلوبة ({quantity}) أكبر من المتاح ({product.quantity})")
                return

            if price <= 0:
                show_error("يرجى إدخال سعر صحيح")
                return

            # إضافة المشترى
            purchase_id = self.db.add_purchase(
                session_id=self.session_id,
                product_id=product.id,
                product_name=product.name,
                quantity=quantity,
                unit_price=price,
                notes=self.notes_text.get(1.0, tk.END).strip()
            )

            self.result = purchase_id
            self.dialog.destroy()

        except Exception as e:
            show_error(f"خطأ في إضافة المشترى: {e}")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()


class SessionDetailsDialog:
    """نافذة عرض تفاصيل الجلسة"""

    def __init__(self, parent, session_id, db, is_active=False):
        self.session_id = session_id
        self.db = db
        self.is_active = is_active

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("تفاصيل الجلسة")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.center_dialog()

        # إنشاء الواجهة
        self.create_interface()

        # تحميل البيانات
        self.load_session_data()

    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 800) // 2
        y = (self.dialog.winfo_screenheight() - 600) // 2
        self.dialog.geometry(f"800x600+{x}+{y}")

    def create_interface(self):
        """إنشاء واجهة النافذة"""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النافذة
        title_label = ttk.Label(
            main_frame,
            text=f"تفاصيل الجلسة #{self.session_id}",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))

        # إطار معلومات الجلسة
        info_frame = ttk.LabelFrame(main_frame, text="معلومات الجلسة", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # إنشاء التسميات
        self.customer_label = ttk.Label(info_frame, text="العميل: ")
        self.customer_label.pack(anchor=tk.W, pady=2)

        self.start_time_label = ttk.Label(info_frame, text="وقت البداية: ")
        self.start_time_label.pack(anchor=tk.W, pady=2)

        self.end_time_label = ttk.Label(info_frame, text="وقت النهاية: ")
        self.end_time_label.pack(anchor=tk.W, pady=2)

        self.duration_label = ttk.Label(info_frame, text="المدة: ")
        self.duration_label.pack(anchor=tk.W, pady=2)

        self.rate_label = ttk.Label(info_frame, text="سعر الساعة: ")
        self.rate_label.pack(anchor=tk.W, pady=2)

        self.total_label = ttk.Label(info_frame, text="إجمالي التكلفة: ")
        self.total_label.pack(anchor=tk.W, pady=2)

        # إطار المشتريات
        purchases_frame = ttk.LabelFrame(main_frame, text="المشتريات", padding=10)
        purchases_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # جدول المشتريات
        columns = ('product', 'quantity', 'unit_price', 'total_price', 'notes')
        self.purchases_tree = ttk.Treeview(purchases_frame, columns=columns, show='headings', height=8)

        # تعريف الأعمدة
        self.purchases_tree.heading('product', text='المنتج')
        self.purchases_tree.heading('quantity', text='الكمية')
        self.purchases_tree.heading('unit_price', text='سعر الوحدة')
        self.purchases_tree.heading('total_price', text='الإجمالي')
        self.purchases_tree.heading('notes', text='ملاحظات')

        # تحديد عرض الأعمدة
        self.purchases_tree.column('product', width=150)
        self.purchases_tree.column('quantity', width=80)
        self.purchases_tree.column('unit_price', width=100)
        self.purchases_tree.column('total_price', width=100)
        self.purchases_tree.column('notes', width=200)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(purchases_frame, orient=tk.VERTICAL, command=self.purchases_tree.yview)
        self.purchases_tree.configure(yscrollcommand=scrollbar.set)

        self.purchases_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار الإجماليات
        totals_frame = ttk.LabelFrame(main_frame, text="الإجماليات", padding=10)
        totals_frame.pack(fill=tk.X, pady=(0, 10))

        self.session_cost_label = ttk.Label(totals_frame, text="تكلفة الجلسة: ")
        self.session_cost_label.pack(anchor=tk.W, pady=2)

        self.purchases_cost_label = ttk.Label(totals_frame, text="تكلفة المشتريات: ")
        self.purchases_cost_label.pack(anchor=tk.W, pady=2)

        self.grand_total_label = ttk.Label(totals_frame, text="الإجمالي الكلي: ", font=('Arial', 12, 'bold'))
        self.grand_total_label.pack(anchor=tk.W, pady=5)

        # زر الإغلاق
        ttk.Button(main_frame, text="إغلاق", command=self.dialog.destroy).pack(pady=10)

    def load_session_data(self):
        """تحميل بيانات الجلسة"""
        try:
            from utils.helpers import format_currency, format_datetime

            # الحصول على بيانات الجلسة
            session = self.db.get_session(self.session_id)
            if not session:
                show_error("لم يتم العثور على الجلسة")
                self.dialog.destroy()
                return

            # تحديث معلومات الجلسة
            self.customer_label.config(text=f"العميل: {session.customer_name}")
            self.start_time_label.config(text=f"وقت البداية: {format_datetime(session.start_time)}")

            if session.end_time:
                self.end_time_label.config(text=f"وقت النهاية: {format_datetime(session.end_time)}")
            else:
                self.end_time_label.config(text="وقت النهاية: جلسة نشطة")

            self.duration_label.config(text=f"المدة: {session.total_hours:.2f} ساعة")
            self.rate_label.config(text=f"سعر الساعة: {format_currency(session.hourly_rate)}")
            self.total_label.config(text=f"إجمالي التكلفة: {format_currency(session.total_amount)}")

            # تحميل المشتريات
            purchases = self.db.get_session_purchases(self.session_id)
            purchases_total = 0

            for purchase in purchases:
                self.purchases_tree.insert('', 'end', values=(
                    purchase.product_name,
                    purchase.quantity,
                    f"{purchase.unit_price:.2f}",
                    f"{purchase.total_price:.2f}",
                    purchase.notes or ""
                ))
                purchases_total += purchase.total_price

            # تحديث الإجماليات
            self.session_cost_label.config(text=f"تكلفة الجلسة: {format_currency(session.total_amount)}")
            self.purchases_cost_label.config(text=f"تكلفة المشتريات: {format_currency(purchases_total)}")

            grand_total = session.total_amount + purchases_total
            self.grand_total_label.config(text=f"الإجمالي الكلي: {format_currency(grand_total)}")

        except Exception as e:
            show_error(f"خطأ في تحميل بيانات الجلسة: {e}")
