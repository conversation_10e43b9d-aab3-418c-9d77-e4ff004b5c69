#!/usr/bin/env python3
"""
Work Space Manager - نظام إدارة مساحة العمل
الملف الرئيسي للتطبيق

تطوير: Python + Tkinter
الإصدار: 1.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# إضافة مسار المشروع لـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gui.main_window import MainWindow
    from utils.config import WINDOW_TITLE
    from utils.helpers import show_error
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
    sys.exit(1)

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        messagebox.showerror(
            "خطأ في الإصدار", 
            "يتطلب هذا التطبيق Python 3.6 أو أحدث\n"
            f"الإصدار الحالي: {sys.version}"
        )
        return False
    return True

def check_dependencies():
    """التحقق من المكتبات المطلوبة"""
    required_modules = ['tkinter', 'sqlite3', 'datetime']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        messagebox.showerror(
            "مكتبات مفقودة",
            f"المكتبات التالية مطلوبة:\n{', '.join(missing_modules)}"
        )
        return False
    return True

def setup_environment():
    """إعداد بيئة التطبيق"""
    try:
        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        data_dir = os.path.join(os.path.dirname(__file__), 'data')
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            print(f"تم إنشاء مجلد البيانات: {data_dir}")
        
        return True
    except Exception as e:
        show_error(f"خطأ في إعداد البيئة: {e}")
        return False

def main():
    """الدالة الرئيسية للتطبيق"""
    print("🚀 بدء تشغيل Work Space Manager...")
    
    # التحقق من المتطلبات
    if not check_python_version():
        return
    
    if not check_dependencies():
        return
    
    if not setup_environment():
        return
    
    try:
        print("📊 تهيئة قاعدة البيانات...")
        print("🎨 إنشاء الواجهة الرئيسية...")
        
        # إنشاء وتشغيل التطبيق
        app = MainWindow()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🎉 مرحباً بك في Work Space Manager!")
        
        # تشغيل حلقة الأحداث الرئيسية
        app.run()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        error_msg = f"خطأ غير متوقع في التطبيق: {e}"
        print(f"❌ {error_msg}")
        
        # عرض رسالة خطأ للمستخدم
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        messagebox.showerror("خطأ في التطبيق", error_msg)
        root.destroy()
    
    print("👋 شكراً لاستخدام Work Space Manager!")

def show_help():
    """عرض معلومات المساعدة"""
    help_text = """
🏢 Work Space Manager - نظام إدارة مساحة العمل

📋 الاستخدام:
    python main.py              تشغيل التطبيق
    python main.py --help       عرض هذه المساعدة
    python main.py --version    عرض معلومات الإصدار

🎯 المميزات:
    • إدارة العملاء والمنتجات
    • تتبع الجلسات والوقت
    • نظام فواتير متكامل
    • تقارير وإحصائيات
    • نسخ احتياطي آمن

🔧 المتطلبات:
    • Python 3.6 أو أحدث
    • مكتبة tkinter (مدمجة عادة)
    • مكتبة sqlite3 (مدمجة عادة)

📞 الدعم:
    في حالة وجود مشاكل، تأكد من:
    1. تثبيت Python بشكل صحيح
    2. وجود جميع الملفات المطلوبة
    3. صلاحيات الكتابة في مجلد التطبيق

© 2024 Work Space Manager - جميع الحقوق محفوظة
    """
    print(help_text)

def show_version():
    """عرض معلومات الإصدار"""
    version_info = f"""
🏢 Work Space Manager
📦 الإصدار: 1.0
🐍 Python: {sys.version}
🖥️ النظام: {sys.platform}
📅 التاريخ: 2024

تطوير: Python + Tkinter
الترخيص: مفتوح المصدر
    """
    print(version_info)

if __name__ == "__main__":
    # معالجة معاملات سطر الأوامر
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['--help', '-h', 'help']:
            show_help()
        elif arg in ['--version', '-v', 'version']:
            show_version()
        else:
            print(f"معامل غير معروف: {sys.argv[1]}")
            print("استخدم --help لعرض المساعدة")
    else:
        # تشغيل التطبيق
        main()
