@echo off
echo ===============================================
echo    Work Space Manager - Python Edition
echo ===============================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.6 أو أحدث من:
    echo https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

echo.
echo 🚀 تشغيل Work Space Manager...
echo.

REM تشغيل التطبيق
python main.py

echo.
echo 👋 شكراً لاستخدام Work Space Manager!
pause
