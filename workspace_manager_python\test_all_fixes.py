#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لجميع الإصلاحات الجديدة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_session_details():
    """اختبار عرض تفاصيل الجلسات"""
    print("🔍 اختبار عرض تفاصيل الجلسات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager()
        
        # الحصول على جلسة منتهية
        completed_sessions = db.get_completed_sessions()
        if completed_sessions:
            session = completed_sessions[0]
            purchases = db.get_session_purchases(session.id)
            
            print(f"✅ جلسة #{session.id}:")
            print(f"   العميل: {session.customer_name}")
            print(f"   المدة: {session.total_hours:.2f} ساعة")
            print(f"   مبلغ الجلسة: {session.total_amount:.2f} جنيه")
            print(f"   عدد المشتريات: {len(purchases)}")
            
            purchases_total = sum(p.total_price for p in purchases)
            print(f"   مبلغ المشتريات: {purchases_total:.2f} جنيه")
            print(f"   الإجمالي الكلي: {session.total_amount + purchases_total:.2f} جنيه")
            
            return True
        else:
            print("❌ لا توجد جلسات منتهية للاختبار")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تفاصيل الجلسات: {e}")
        return False

def test_purchases_in_total():
    """اختبار إضافة المشتريات للإجمالي"""
    print("\n🛒 اختبار إضافة المشتريات للإجمالي...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager()
        
        # الحصول على جلسة نشطة أو إنشاء واحدة
        active_sessions = db.get_active_sessions()
        
        if not active_sessions:
            # إنشاء جلسة جديدة للاختبار
            customers = db.get_customers()
            if customers:
                customer = customers[0]
                session_id = db.start_session(
                    customer_id=customer.id,
                    customer_name=customer.name,
                    hourly_rate=20.0
                )
                print(f"✅ تم إنشاء جلسة جديدة: ID={session_id}")
            else:
                print("❌ لا توجد عملاء للاختبار")
                return False
        else:
            session_id = active_sessions[0].id
            print(f"✅ استخدام جلسة نشطة: ID={session_id}")
        
        # إضافة مشترى للاختبار
        products = db.get_products()
        if products:
            product = products[0]
            purchase_id = db.add_purchase(
                session_id=session_id,
                product_id=product.id,
                product_name=product.name,
                quantity=2,
                unit_price=product.price + 5.0,  # سعر مخصص
                notes="مشترى اختبار"
            )
            print(f"✅ تم إضافة مشترى: ID={purchase_id}")
            
            # التحقق من الإجمالي
            purchases = db.get_session_purchases(session_id)
            purchases_total = sum(p.total_price for p in purchases)
            print(f"✅ إجمالي المشتريات: {purchases_total:.2f} جنيه")
            
            return True
        else:
            print("❌ لا توجد منتجات للاختبار")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المشتريات: {e}")
        return False

def test_expenses():
    """اختبار صفحة المصروفات"""
    print("\n💰 اختبار صفحة المصروفات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager()
        
        # الحصول على المصروفات
        expenses = db.get_expenses()
        print(f"✅ عدد المصروفات: {len(expenses)}")
        
        if expenses:
            total_expenses = sum(e.amount for e in expenses)
            print(f"✅ إجمالي المصروفات: {total_expenses:.2f} جنيه")
            
            # عرض أول 3 مصروفات
            for i, expense in enumerate(expenses[:3]):
                print(f"   {i+1}. {expense.type}: {expense.description} - {expense.amount:.2f} جنيه")
        
        # إضافة مصروف جديد للاختبار
        expense_id = db.add_expense(
            type="اختبار",
            description="مصروف تجريبي للاختبار",
            amount=50.0,
            date="2023-12-10",
            notes="مصروف للاختبار فقط"
        )
        print(f"✅ تم إضافة مصروف جديد: ID={expense_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المصروفات: {e}")
        return False

def test_invoice_editing():
    """اختبار تعديل الفواتير"""
    print("\n🧾 اختبار تعديل الفواتير...")
    
    try:
        from database.database_manager import DatabaseManager
        
        db = DatabaseManager()
        
        # الحصول على فاتورة موجودة
        invoices = db.get_invoices()
        
        if invoices:
            invoice = invoices[0]
            print(f"✅ فاتورة #{invoice.id}:")
            print(f"   المبلغ الأصلي: {invoice.final_amount:.2f} جنيه")
            print(f"   المدفوع الأصلي: {invoice.paid_amount:.2f} جنيه")
            
            # تعديل الفاتورة
            new_paid_amount = invoice.final_amount  # دفع كامل
            db.update_invoice(
                invoice_id=invoice.id,
                session_amount=invoice.session_amount,
                purchases_amount=invoice.purchases_amount,
                discount=invoice.discount,
                discount_type=invoice.discount_type,
                paid_amount=new_paid_amount,
                payment_method="بطاقة",
                notes=invoice.notes + " - تم التعديل في الاختبار"
            )
            
            print(f"✅ تم تعديل الفاتورة - المدفوع الجديد: {new_paid_amount:.2f} جنيه")
            return True
        else:
            print("❌ لا توجد فواتير للاختبار")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار تعديل الفواتير: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء الاختبار الشامل لجميع الإصلاحات...")
    print("="*60)
    
    tests = [
        ("عرض تفاصيل الجلسات", test_session_details),
        ("إضافة المشتريات للإجمالي", test_purchases_in_total),
        ("صفحة المصروفات", test_expenses),
        ("تعديل الفواتير", test_invoice_editing)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n✅ الميزات التي تعمل:")
        print("   - عرض تفاصيل الجلسات السابقة")
        print("   - إضافة المشتريات للتكلفة الإجمالية")
        print("   - صفحة المصروفات مع البيانات")
        print("   - تعديل الفواتير وحفظ التغييرات")
        print("\n🎯 يمكنك الآن استخدام جميع الميزات الجديدة!")
    else:
        print(f"⚠️ {total - passed} اختبارات فشلت")
        print("يرجى مراجعة رسائل الخطأ أعلاه")

if __name__ == "__main__":
    main()
