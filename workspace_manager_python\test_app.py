#!/usr/bin/env python3
"""
اختبار بسيط للتطبيق
Simple test for the application
"""

import sys
import os

print("🧪 اختبار Work Space Manager...")
print(f"🐍 Python Version: {sys.version}")
print(f"📁 Current Directory: {os.getcwd()}")

try:
    # اختبار استيراد المكتبات الأساسية
    import tkinter as tk
    print("✅ tkinter imported successfully")
    
    import sqlite3
    print("✅ sqlite3 imported successfully")
    
    # اختبار استيراد وحدات التطبيق
    from database.database_manager import DatabaseManager
    print("✅ DatabaseManager imported successfully")
    
    from utils.config import WINDOW_TITLE
    print("✅ Config imported successfully")
    
    from utils.helpers import show_success
    print("✅ Helpers imported successfully")
    
    # اختبار قاعدة البيانات
    db = DatabaseManager()
    print("✅ Database initialized successfully")
    
    # اختبار الحصول على العملاء
    customers = db.get_customers()
    print(f"✅ Found {len(customers)} customers in database")
    
    # اختبار الحصول على المنتجات
    products = db.get_products()
    print(f"✅ Found {len(products)} products in database")
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("✅ التطبيق جاهز للتشغيل")
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🚀 يمكنك الآن تشغيل التطبيق باستخدام:")
print("   python main.py")
