#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار شامل لنظام الفواتير المحسن
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_invoice_system():
    """اختبار شامل لنظام الفواتير"""
    
    print("🧮 اختبار نظام الفواتير المحسن...")
    print("="*60)
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # 1. التحقق من وجود العملاء والمنتجات
        customers = db.get_customers()
        products = db.get_products()
        active_products = [p for p in products if p.is_active and p.quantity > 0]
        
        print(f"📊 الإحصائيات:")
        print(f"   العملاء: {len(customers)}")
        print(f"   المنتجات النشطة: {len(active_products)}")
        
        if not customers or not active_products:
            print("❌ لا توجد عملاء أو منتجات متاحة!")
            return False
        
        customer = customers[0]
        product1 = active_products[0]
        product2 = active_products[1] if len(active_products) > 1 else active_products[0]
        
        # 2. بدء جلسة جديدة
        print(f"\n🎮 بدء جلسة للعميل: {customer.name}")
        session_id = db.start_session(
            customer_id=customer.id,
            customer_name=customer.name,
            hourly_rate=25.0,
            daily_rate=0,
            pricing_type="hourly"
        )
        print(f"✅ تم بدء الجلسة: ID={session_id}")
        
        # 3. إضافة مشتريات متعددة بأسعار مختلفة
        print(f"\n🛒 إضافة مشتريات...")
        
        # مشترى أول بالسعر الأصلي
        purchase1_id = db.add_purchase(
            session_id=session_id,
            product_id=product1.id,
            product_name=product1.name,
            quantity=3,
            unit_price=product1.price,
            notes="مشترى بالسعر الأصلي"
        )
        purchase1_total = product1.price * 3
        print(f"✅ مشترى أول: {product1.name} x 3 = {purchase1_total:.2f} جنيه")
        
        # مشترى ثاني بسعر مخصص (زيادة)
        custom_price = product2.price + 10.0
        purchase2_id = db.add_purchase(
            session_id=session_id,
            product_id=product2.id,
            product_name=product2.name,
            quantity=2,
            unit_price=custom_price,
            notes="مشترى بسعر مخصص (زيادة)"
        )
        purchase2_total = custom_price * 2
        print(f"✅ مشترى ثاني: {product2.name} x 2 = {purchase2_total:.2f} جنيه (سعر مخصص)")
        
        # 4. التحقق من إجمالي المشتريات
        purchases = db.get_session_purchases(session_id)
        total_purchases = sum(p.total_price for p in purchases)
        print(f"\n📋 إجمالي المشتريات: {total_purchases:.2f} جنيه")
        
        # 5. إنهاء الجلسة بعد وقت قصير
        print(f"\n🏁 إنهاء الجلسة...")
        import time
        time.sleep(3)  # انتظار 3 ثوان
        
        ended_session = db.end_session(session_id)
        if ended_session:
            print(f"✅ تم إنهاء الجلسة:")
            print(f"   المدة: {ended_session.total_hours:.2f} ساعة")
            print(f"   مبلغ الجلسة: {ended_session.total_amount:.2f} جنيه")
        
        # 6. حساب الإجمالي الطبيعي
        natural_total = ended_session.total_amount + total_purchases
        print(f"\n💰 الحسابات:")
        print(f"   مبلغ الجلسة: {ended_session.total_amount:.2f} جنيه")
        print(f"   مبلغ المشتريات: {total_purchases:.2f} جنيه")
        print(f"   الإجمالي الطبيعي: {natural_total:.2f} جنيه")
        
        # 7. إنشاء فاتورة بمبلغ مخصص
        custom_final_amount = natural_total + 15.0  # زيادة 15 جنيه
        print(f"   المبلغ المخصص: {custom_final_amount:.2f} جنيه (زيادة 15 جنيه)")
        
        invoice_id = db.create_invoice_with_custom_amount(
            session_id=session_id,
            custom_final_amount=custom_final_amount,
            discount=10.0,  # خصم 10 جنيه
            discount_type="مبلغ",
            paid_amount=100.0,
            payment_method="نقدي",
            notes="فاتورة اختبار بمبلغ مخصص"
        )
        
        print(f"\n🧾 تم إنشاء الفاتورة: ID={invoice_id}")
        
        # 8. التحقق من تفاصيل الفاتورة
        invoices = db.get_invoices()
        invoice = next((inv for inv in invoices if inv.id == invoice_id), None)
        
        if invoice:
            print(f"\n📄 تفاصيل الفاتورة:")
            print(f"   مبلغ الجلسة: {invoice.session_amount:.2f} جنيه")
            print(f"   مبلغ المشتريات: {invoice.purchases_amount:.2f} جنيه")
            print(f"   الإجمالي: {invoice.total_amount:.2f} جنيه")
            print(f"   الخصم: {invoice.discount:.2f} جنيه")
            print(f"   المبلغ النهائي: {invoice.final_amount:.2f} جنيه")
            print(f"   المدفوع: {invoice.paid_amount:.2f} جنيه")
            print(f"   المتبقي: {invoice.remaining_amount:.2f} جنيه")
            print(f"   حالة الدفع: {invoice.payment_status}")
            
            # التحقق من صحة الحسابات
            expected_final = custom_final_amount - invoice.discount
            expected_remaining = expected_final - invoice.paid_amount
            
            print(f"\n🔍 التحقق من الحسابات:")
            print(f"   المبلغ النهائي المتوقع: {expected_final:.2f} جنيه")
            print(f"   المتبقي المتوقع: {expected_remaining:.2f} جنيه")
            
            if (abs(invoice.final_amount - expected_final) < 0.01 and
                abs(invoice.remaining_amount - expected_remaining) < 0.01):
                print("✅ جميع الحسابات صحيحة!")
                
                # 9. اختبار تحديث الدفعة
                print(f"\n💳 اختبار تحديث الدفعة...")
                new_paid_amount = invoice.final_amount  # دفع كامل
                db.update_invoice_payment(invoice_id, new_paid_amount, "بطاقة")
                
                # التحقق من التحديث
                updated_invoices = db.get_invoices()
                updated_invoice = next((inv for inv in updated_invoices if inv.id == invoice_id), None)
                
                if updated_invoice and updated_invoice.payment_status == "مدفوع":
                    print("✅ تم تحديث الدفعة بنجاح!")
                    return True
                else:
                    print("❌ فشل في تحديث الدفعة!")
                    return False
            else:
                print("❌ هناك خطأ في الحسابات!")
                return False
        else:
            print("❌ لم يتم العثور على الفاتورة!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء الاختبار الشامل لنظام الفواتير المحسن...")
    print("="*60)
    
    success = test_complete_invoice_system()
    
    print("\n" + "="*60)
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n✅ الميزات التي تعمل:")
        print("   - إضافة المشتريات بأسعار مخصصة")
        print("   - حساب المشتريات في إجمالي الفاتورة")
        print("   - تعديل المبلغ النهائي يدوياً")
        print("   - حفظ الفواتير بنجاح")
        print("   - تحديث دفعات الفواتير")
        print("   - عرض الجلسات المنتهية (مفوترة وغير مفوترة)")
        print("\n🎯 يمكنك الآن استخدام التطبيق بثقة!")
    else:
        print("❌ فشل في بعض الاختبارات!")
        print("يرجى مراجعة رسائل الخطأ أعلاه")

if __name__ == "__main__":
    main()
