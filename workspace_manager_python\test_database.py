"""
اختبار قاعدة البيانات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager
from database.models import Customer

def test_database():
    """اختبار قاعدة البيانات"""
    
    print("=== اختبار قاعدة البيانات ===")
    
    try:
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات بنجاح")
        
        # اختبار الحصول على العملاء
        print("\n--- اختبار الحصول على العملاء ---")
        customers = db.get_customers()
        print(f"عدد العملاء: {len(customers)}")
        
        if customers:
            for customer in customers[:3]:  # عرض أول 3 عملاء
                print(f"العميل: {customer.name} (ID: {customer.id})")
        else:
            print("لا توجد عملاء، سيتم إضافة عميل تجريبي...")
            
            # إضافة عميل تجريبي
            test_customer = Customer(
                name="عميل تجريبي",
                phone="01234567890",
                email="<EMAIL>",
                notes="عميل للاختبار"
            )
            
            customer_id = db.add_customer(test_customer)
            print(f"تم إضافة عميل تجريبي: ID={customer_id}")
            
            # إعادة الحصول على العملاء
            customers = db.get_customers()
            print(f"عدد العملاء بعد الإضافة: {len(customers)}")
        
        # اختبار الحصول على الجلسات النشطة
        print("\n--- اختبار الحصول على الجلسات النشطة ---")
        active_sessions = db.get_active_sessions()
        print(f"عدد الجلسات النشطة: {len(active_sessions)}")
        
        if active_sessions:
            for session in active_sessions:
                print(f"الجلسة: ID={session.id}, العميل={session.customer_name}")
        
        # اختبار الحصول على المنتجات
        print("\n--- اختبار الحصول على المنتجات ---")
        products = db.get_products()
        print(f"عدد المنتجات: {len(products)}")
        
        if products:
            for product in products[:3]:  # عرض أول 3 منتجات
                print(f"المنتج: {product.name}, السعر: {product.price}, الكمية: {product.quantity}")
        
        print("\n✅ جميع الاختبارات تمت بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database()
