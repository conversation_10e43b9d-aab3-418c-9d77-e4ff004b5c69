"""
اختبار الإصلاحات الجديدة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager
from database.models import Customer, Product

def test_fixes():
    """اختبار الإصلاحات"""
    
    print("=== اختبار الإصلاحات الجديدة ===")
    
    try:
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات بنجاح")
        
        # اختبار الحصول على العملاء
        print("\n--- اختبار العملاء ---")
        customers = db.get_customers()
        print(f"عدد العملاء: {len(customers)}")
        
        if not customers:
            # إضافة عميل تجريبي
            test_customer = Customer(
                name="عميل تجريبي",
                phone="01234567890",
                email="<EMAIL>",
                notes="عميل للاختبار"
            )
            customer_id = db.add_customer(test_customer)
            print(f"تم إضافة عميل تجريبي: ID={customer_id}")
            customers = db.get_customers()
        
        # اختبار الحصول على المنتجات
        print("\n--- اختبار المنتجات ---")
        products = db.get_products()
        print(f"عدد المنتجات: {len(products)}")
        
        if not products:
            # إضافة منتج تجريبي
            test_product = Product(
                name="منتج تجريبي",
                category="تجريبي",
                price=10.0,
                cost=5.0,
                quantity=100,
                description="منتج للاختبار"
            )
            product_id = db.add_product(test_product)
            print(f"تم إضافة منتج تجريبي: ID={product_id}")
            products = db.get_products()
        
        # اختبار بدء جلسة
        print("\n--- اختبار بدء الجلسة ---")
        if customers:
            customer = customers[0]
            print(f"محاولة بدء جلسة للعميل: {customer.name}")
            
            try:
                session_id = db.start_session(
                    customer_id=customer.id,
                    customer_name=customer.name,
                    hourly_rate=15.0,
                    daily_rate=0,
                    pricing_type="hourly"
                )
                print(f"✅ تم بدء الجلسة بنجاح: ID={session_id}")
                
                # اختبار الحصول على الجلسات النشطة
                active_sessions = db.get_active_sessions()
                print(f"عدد الجلسات النشطة: {len(active_sessions)}")
                
                if active_sessions:
                    session = active_sessions[0]
                    print(f"الجلسة النشطة: ID={session.id}, العميل={session.customer_name}")
                    
                    # اختبار إنهاء الجلسة
                    print("محاولة إنهاء الجلسة...")
                    ended_session = db.end_session(session.id)
                    if ended_session:
                        print(f"✅ تم إنهاء الجلسة بنجاح: المدة={ended_session.total_hours:.2f} ساعة")
                    else:
                        print("❌ فشل في إنهاء الجلسة")
                
            except Exception as e:
                print(f"❌ خطأ في بدء الجلسة: {e}")
        
        print("\n✅ تم اختبار جميع الوظائف الأساسية!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixes()
