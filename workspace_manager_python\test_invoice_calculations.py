#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار حسابات الفواتير والمشتريات
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_invoice_calculations():
    """اختبار حسابات الفواتير"""
    
    print("🧮 اختبار حسابات الفواتير والمشتريات...")
    
    try:
        from database.database_manager import DatabaseManager
        
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # 1. الحصول على العملاء والمنتجات
        customers = db.get_customers()
        products = db.get_products()
        active_products = [p for p in products if p.is_active and p.quantity > 0]
        
        if not customers or not active_products:
            print("❌ لا توجد عملاء أو منتجات متاحة!")
            return False
        
        customer = customers[0]
        product1 = active_products[0]
        product2 = active_products[1] if len(active_products) > 1 else active_products[0]
        
        print(f"العميل: {customer.name}")
        print(f"المنتج الأول: {product1.name} - {product1.price} جنيه")
        print(f"المنتج الثاني: {product2.name} - {product2.price} جنيه")
        
        # 2. بدء جلسة
        print("\n🎮 بدء جلسة...")
        session_id = db.start_session(
            customer_id=customer.id,
            customer_name=customer.name,
            hourly_rate=20.0,
            daily_rate=0,
            pricing_type="hourly"
        )
        print(f"✅ تم بدء الجلسة: ID={session_id}")
        
        # 3. إضافة مشتريات متعددة
        print("\n🛒 إضافة مشتريات...")
        
        # مشترى أول
        purchase1_id = db.add_purchase(
            session_id=session_id,
            product_id=product1.id,
            product_name=product1.name,
            quantity=2,
            unit_price=product1.price,
            notes="مشترى أول"
        )
        print(f"✅ مشترى أول: {product1.name} x 2 = {product1.price * 2:.2f} جنيه")
        
        # مشترى ثاني بسعر مخصص
        custom_price = product2.price + 5.0  # زيادة 5 جنيه
        purchase2_id = db.add_purchase(
            session_id=session_id,
            product_id=product2.id,
            product_name=product2.name,
            quantity=1,
            unit_price=custom_price,
            notes="مشترى بسعر مخصص"
        )
        print(f"✅ مشترى ثاني: {product2.name} x 1 = {custom_price:.2f} جنيه (سعر مخصص)")
        
        # 4. التحقق من المشتريات
        print("\n📋 التحقق من المشتريات...")
        purchases = db.get_session_purchases(session_id)
        total_purchases = sum(p.total_price for p in purchases)
        
        print(f"عدد المشتريات: {len(purchases)}")
        for p in purchases:
            print(f"  - {p.product_name}: {p.quantity} x {p.unit_price:.2f} = {p.total_price:.2f} جنيه")
        print(f"إجمالي المشتريات: {total_purchases:.2f} جنيه")
        
        # 5. إنهاء الجلسة
        print("\n🏁 إنهاء الجلسة...")
        import time
        time.sleep(2)  # انتظار ثانيتين لحساب الوقت
        
        ended_session = db.end_session(session_id)
        if ended_session:
            print(f"✅ تم إنهاء الجلسة:")
            print(f"   المدة: {ended_session.total_hours:.2f} ساعة")
            print(f"   مبلغ الجلسة: {ended_session.total_amount:.2f} جنيه")
        
        # 6. إنشاء فاتورة
        print("\n🧾 إنشاء فاتورة...")
        invoice_id = db.create_invoice(
            session_id=session_id,
            discount=5.0,  # خصم 5 جنيه
            discount_type="مبلغ",
            paid_amount=50.0,
            payment_method="نقدي",
            notes="فاتورة اختبار"
        )
        
        print(f"✅ تم إنشاء الفاتورة: ID={invoice_id}")
        
        # 7. التحقق من حسابات الفاتورة
        print("\n💰 تفاصيل الفاتورة:")
        invoices = db.get_invoices()
        invoice = next((inv for inv in invoices if inv.id == invoice_id), None)
        
        if invoice:
            print(f"   مبلغ الجلسة: {invoice.session_amount:.2f} جنيه")
            print(f"   مبلغ المشتريات: {invoice.purchases_amount:.2f} جنيه")
            print(f"   الإجمالي قبل الخصم: {invoice.total_amount:.2f} جنيه")
            print(f"   الخصم: {invoice.discount:.2f} جنيه")
            print(f"   المبلغ النهائي: {invoice.final_amount:.2f} جنيه")
            print(f"   المدفوع: {invoice.paid_amount:.2f} جنيه")
            print(f"   المتبقي: {invoice.remaining_amount:.2f} جنيه")
            print(f"   حالة الدفع: {invoice.payment_status}")
            
            # التحقق من صحة الحسابات
            expected_total = ended_session.total_amount + total_purchases
            expected_final = expected_total - invoice.discount
            expected_remaining = expected_final - invoice.paid_amount
            
            print(f"\n🔍 التحقق من الحسابات:")
            print(f"   الإجمالي المتوقع: {expected_total:.2f} جنيه")
            print(f"   المبلغ النهائي المتوقع: {expected_final:.2f} جنيه")
            print(f"   المتبقي المتوقع: {expected_remaining:.2f} جنيه")
            
            if (abs(invoice.total_amount - expected_total) < 0.01 and
                abs(invoice.final_amount - expected_final) < 0.01 and
                abs(invoice.remaining_amount - expected_remaining) < 0.01):
                print("✅ جميع الحسابات صحيحة!")
                return True
            else:
                print("❌ هناك خطأ في الحسابات!")
                return False
        else:
            print("❌ لم يتم العثور على الفاتورة!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء اختبار حسابات الفواتير...")
    print("="*60)
    
    success = test_invoice_calculations()
    
    print("\n" + "="*60)
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ حسابات المشتريات تعمل بشكل صحيح")
        print("✅ حسابات الفواتير تعمل بشكل صحيح")
        print("✅ يمكن تعديل أسعار المنتجات يدوياً")
    else:
        print("❌ فشل في بعض الاختبارات!")
        print("يرجى مراجعة رسائل الخطأ أعلاه")

if __name__ == "__main__":
    main()
