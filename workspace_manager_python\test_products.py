#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager
from database.models import Product

def test_products():
    """اختبار المنتجات"""
    
    print("=== اختبار المنتجات ===")
    
    try:
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        print("تم إنشاء قاعدة البيانات")
        
        # الحصول على المنتجات
        products = db.get_products()
        print(f"عدد المنتجات: {len(products)}")
        
        if not products:
            print("لا توجد منتجات، سأضيف منتجات تجريبية...")
            
            # إضافة منتجات تجريبية
            test_products = [
                Product(
                    name="شاي",
                    category="مشروبات",
                    price=5.0,
                    cost=2.0,
                    quantity=50,
                    description="شاي ساخن"
                ),
                Product(
                    name="قهوة",
                    category="مشروبات",
                    price=8.0,
                    cost=3.0,
                    quantity=30,
                    description="قهوة تركية"
                ),
                Product(
                    name="عصير برتقال",
                    category="مشروبات",
                    price=10.0,
                    cost=4.0,
                    quantity=20,
                    description="عصير برتقال طازج"
                )
            ]
            
            for product in test_products:
                product_id = db.add_product(product)
                print(f"تم إضافة منتج: {product.name} - ID={product_id}")
            
            # إعادة جلب المنتجات
            products = db.get_products()
            print(f"عدد المنتجات بعد الإضافة: {len(products)}")
        
        # عرض تفاصيل المنتجات
        print("\n--- تفاصيل المنتجات ---")
        for p in products:
            print(f"المنتج: {p.name}")
            print(f"  الفئة: {p.category}")
            print(f"  السعر: {p.price} جنيه")
            print(f"  التكلفة: {p.cost} جنيه")
            print(f"  الكمية: {p.quantity}")
            print(f"  نشط: {p.is_active}")
            print(f"  الوصف: {p.description}")
            print("---")
            
    except Exception as e:
        print(f"خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_products()
