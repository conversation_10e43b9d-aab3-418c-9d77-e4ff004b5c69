#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نافذة إضافة المشتريات
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_purchase_dialog():
    """اختبار نافذة إضافة المشتريات"""
    
    try:
        from database.database_manager import DatabaseManager
        from gui.sessions import PurchaseDialog
        
        # إنشاء مدير قاعدة البيانات
        db = DatabaseManager()
        
        # إنشاء نافذة رئيسية
        root = tk.Tk()
        root.title("اختبار نافذة المشتريات")
        root.geometry("300x200")
        
        # إنشاء زر لفتح نافذة المشتريات
        def open_purchase_dialog():
            # استخدام session_id تجريبي
            dialog = PurchaseDialog(root, "اختبار إضافة مشترى", db, 1)
            if dialog.result:
                print(f"تم إضافة مشترى: ID={dialog.result}")
            else:
                print("تم إلغاء العملية")
        
        test_button = ttk.Button(
            root,
            text="فتح نافذة إضافة المشتريات",
            command=open_purchase_dialog
        )
        test_button.pack(expand=True)
        
        # تشغيل النافذة
        root.mainloop()
        
    except Exception as e:
        print(f"خطأ في اختبار النافذة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_purchase_dialog()
