#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار وظائف السكرول في جميع أجزاء البرنامج
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_scroll_helper():
    """اختبار مساعد السكرول"""
    print("🖱️ اختبار مساعد السكرول...")
    
    try:
        from utils.scroll_helper import (
            bind_mousewheel_to_widget,
            make_scrollable_frame,
            add_scroll_to_treeview,
            add_scroll_to_text,
            add_scroll_to_listbox,
            add_scroll_to_combobox,
            enable_scroll_for_all_children,
            make_everything_scrollable
        )
        
        print("✅ تم استيراد جميع دوال السكرول بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد مساعد السكرول: {e}")
        return False

def test_scroll_in_gui_modules():
    """اختبار إضافة السكرول في وحدات الواجهة"""
    print("\n🖼️ اختبار إضافة السكرول في وحدات الواجهة...")
    
    modules_to_test = [
        "gui.sessions",
        "gui.invoices", 
        "gui.expenses",
        "gui.customers",
        "gui.products",
        "gui.reports",
        "gui.dashboard",
        "gui.main_window"
    ]
    
    passed = 0
    total = len(modules_to_test)
    
    for module_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[''])
            
            # التحقق من وجود استيراد scroll_helper
            module_file = module.__file__
            with open(module_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'scroll_helper' in content:
                print(f"✅ {module_name}: يحتوي على دعم السكرول")
                passed += 1
            else:
                print(f"❌ {module_name}: لا يحتوي على دعم السكرول")
                
        except Exception as e:
            print(f"❌ {module_name}: خطأ في الاختبار - {e}")
    
    print(f"\n📊 النتيجة: {passed}/{total} وحدات تدعم السكرول")
    return passed == total

def test_scroll_demo():
    """عرض توضيحي لوظائف السكرول"""
    print("\n🎮 عرض توضيحي لوظائف السكرول...")
    
    try:
        import tkinter as tk
        from tkinter import ttk
        from utils.scroll_helper import (
            make_scrollable_frame,
            add_scroll_to_treeview,
            add_scroll_to_text,
            add_scroll_to_listbox,
            add_scroll_to_combobox,
            make_everything_scrollable
        )
        
        # إنشاء نافذة تجريبية
        root = tk.Tk()
        root.title("اختبار السكرول")
        root.geometry("600x400")
        
        # إنشاء دفتر علامات التبويب
        notebook = ttk.Notebook(root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # علامة تبويب للـ Treeview
        tree_frame = ttk.Frame(notebook)
        notebook.add(tree_frame, text="Treeview")
        
        tree = ttk.Treeview(tree_frame, columns=('col1', 'col2'), show='headings')
        tree.heading('col1', text='العمود الأول')
        tree.heading('col2', text='العمود الثاني')
        
        # إضافة بيانات كثيرة للاختبار
        for i in range(100):
            tree.insert('', 'end', values=(f'بيانات {i+1}', f'قيمة {i+1}'))
        
        tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        add_scroll_to_treeview(tree)
        
        # علامة تبويب للـ Text
        text_frame = ttk.Frame(notebook)
        notebook.add(text_frame, text="Text")
        
        text_widget = tk.Text(text_frame, wrap=tk.WORD)
        
        # إضافة نص طويل للاختبار
        long_text = "\n".join([f"هذا السطر رقم {i+1} من النص الطويل للاختبار." for i in range(200)])
        text_widget.insert(1.0, long_text)
        
        text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        add_scroll_to_text(text_widget)
        
        # علامة تبويب للـ Listbox
        list_frame = ttk.Frame(notebook)
        notebook.add(list_frame, text="Listbox")
        
        listbox = tk.Listbox(list_frame)
        
        # إضافة عناصر كثيرة للاختبار
        for i in range(100):
            listbox.insert(tk.END, f"عنصر رقم {i+1}")
        
        listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        add_scroll_to_listbox(listbox)
        
        # علامة تبويب للـ Combobox
        combo_frame = ttk.Frame(notebook)
        notebook.add(combo_frame, text="Combobox")
        
        ttk.Label(combo_frame, text="جرب السكرول على القائمة المنسدلة:").pack(pady=10)
        
        combo_values = [f"خيار رقم {i+1}" for i in range(50)]
        combobox = ttk.Combobox(combo_frame, values=combo_values, state="readonly")
        combobox.pack(pady=10)
        combobox.current(0)
        add_scroll_to_combobox(combobox)
        
        # علامة تبويب للإطار القابل للسكرول
        scroll_frame_tab = ttk.Frame(notebook)
        notebook.add(scroll_frame_tab, text="Scrollable Frame")
        
        main_frame, scrollable_frame = make_scrollable_frame(scroll_frame_tab)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # إضافة محتوى كثير للإطار القابل للسكرول
        for i in range(50):
            ttk.Label(scrollable_frame, text=f"تسمية رقم {i+1} - هذا نص طويل للاختبار").pack(pady=2, anchor=tk.W)
            ttk.Button(scrollable_frame, text=f"زر رقم {i+1}").pack(pady=2, anchor=tk.W)
        
        # تطبيق السكرول على كل شيء
        make_everything_scrollable(root)
        
        # إضافة تعليمات
        instructions = tk.Toplevel(root)
        instructions.title("تعليمات الاختبار")
        instructions.geometry("400x300")
        
        instructions_text = """
🖱️ تعليمات اختبار السكرول:

1. استخدم عجلة الماوس للسكرول في أي علامة تبويب
2. في Treeview: سكرول عبر الصفوف
3. في Text: سكرول عبر النص الطويل  
4. في Listbox: سكرول عبر العناصر
5. في Combobox: سكرول لتغيير الاختيار
6. في Scrollable Frame: سكرول عبر المحتوى

✅ إذا كان السكرول يعمل في جميع العلامات، 
   فإن النظام يدعم السكرول بشكل صحيح!

❌ إذا لم يعمل السكرول، تحقق من:
   - وجود ملف scroll_helper.py
   - استيراد الوحدات بشكل صحيح
   - إضافة السكرول في جميع الصفحات
        """
        
        instructions_label = tk.Label(
            instructions, 
            text=instructions_text, 
            justify=tk.LEFT,
            wraplength=350
        )
        instructions_label.pack(padx=10, pady=10)
        
        print("✅ تم فتح نافذة العرض التوضيحي")
        print("🖱️ جرب السكرول بعجلة الماوس في جميع علامات التبويب")
        
        # تشغيل النافذة
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في العرض التوضيحي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء اختبار وظائف السكرول...")
    print("="*60)
    
    tests = [
        ("مساعد السكرول", test_scroll_helper),
        ("وحدات الواجهة", test_scroll_in_gui_modules),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {e}")
    
    print("\n" + "="*60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع اختبارات السكرول نجحت!")
        print("\n✅ الميزات المدعومة:")
        print("   - السكرول في Treeview (الجداول)")
        print("   - السكرول في Text (النصوص الطويلة)")
        print("   - السكرول في Listbox (القوائم)")
        print("   - السكرول في Combobox (القوائم المنسدلة)")
        print("   - السكرول في الإطارات القابلة للسكرول")
        print("   - السكرول التلقائي لجميع العناصر")
        
        # عرض العرض التوضيحي
        print("\n🎮 هل تريد رؤية العرض التوضيحي؟ (y/n)")
        try:
            choice = input().lower()
            if choice in ['y', 'yes', 'نعم', 'ن']:
                test_scroll_demo()
        except:
            pass
    else:
        print(f"⚠️ {total - passed} اختبارات فشلت")
        print("يرجى مراجعة رسائل الخطأ أعلاه")

if __name__ == "__main__":
    main()
