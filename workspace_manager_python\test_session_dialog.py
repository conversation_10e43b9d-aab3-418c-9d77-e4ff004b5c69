"""
اختبار نافذة بدء الجلسة
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.dashboard import SessionStartDialog

def test_session_dialog():
    """اختبار نافذة بدء الجلسة"""
    
    # إنشاء نافذة رئيسية
    root = tk.Tk()
    root.title("اختبار نافذة بدء الجلسة")
    root.geometry("300x200")
    
    def open_dialog():
        """فتح نافذة بدء الجلسة"""
        dialog = SessionStartDialog(root, "بدء جلسة جديدة", "أحمد محمد")
        
        # انتظار إغلاق النافذة
        root.wait_window(dialog.dialog)
        
        # عرض النتيجة
        if dialog.result:
            result_label.config(text=f"النتيجة: {dialog.result}")
            print(f"نتيجة النافذة: {dialog.result}")
        else:
            result_label.config(text="تم الإلغاء")
            print("تم إلغاء النافذة")
    
    # إنشاء واجهة الاختبار
    main_frame = ttk.Frame(root, padding=20)
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(main_frame, text="اختبار نافذة بدء الجلسة", font=('Arial', 14, 'bold')).pack(pady=10)
    
    ttk.Button(main_frame, text="🚀 فتح نافذة بدء الجلسة", command=open_dialog).pack(pady=10)
    
    result_label = ttk.Label(main_frame, text="لم يتم فتح النافذة بعد", foreground="blue")
    result_label.pack(pady=10)
    
    ttk.Button(main_frame, text="❌ إغلاق", command=root.destroy).pack(pady=10)
    
    # تشغيل التطبيق
    root.mainloop()

if __name__ == "__main__":
    test_session_dialog()
