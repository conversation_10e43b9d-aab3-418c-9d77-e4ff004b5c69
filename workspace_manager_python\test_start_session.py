"""
اختبار بدء الجلسة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database.database_manager import DatabaseManager

def test_start_session():
    """اختبار بدء جلسة"""
    
    print("=== اختبار بدء الجلسة ===")
    
    # إنشاء مدير قاعدة البيانات
    db = DatabaseManager()
    
    try:
        # الحصول على العملاء
        customers = db.get_customers()
        print(f"عدد العملاء: {len(customers)}")
        
        if customers:
            customer = customers[0]
            print(f"العميل المختار: {customer.name} (ID: {customer.id})")
            
            # التحقق من الجلسات النشطة
            active_sessions = db.get_active_sessions()
            print(f"عدد الجلسات النشطة قبل البدء: {len(active_sessions)}")
            
            # بدء جلسة جديدة
            print("\n--- بدء جلسة جديدة ---")
            session_id = db.start_session(
                customer_id=customer.id,
                customer_name=customer.name,
                hourly_rate=15.0,
                daily_rate=0,
                pricing_type="hourly"
            )
            
            print(f"تم إنشاء الجلسة: {session_id}")
            
            # التحقق من الجلسات النشطة بعد البدء
            active_sessions_after = db.get_active_sessions()
            print(f"عدد الجلسات النشطة بعد البدء: {len(active_sessions_after)}")
            
            if active_sessions_after:
                for session in active_sessions_after:
                    print(f"الجلسة: ID={session.id}, العميل={session.customer_name}, السعر={session.hourly_rate}")
            
            # الحصول على الجلسة المحددة
            session = db.get_session(session_id)
            if session:
                print(f"تفاصيل الجلسة: {session}")
            else:
                print("خطأ: لم يتم العثور على الجلسة!")
                
        else:
            print("لا توجد عملاء في قاعدة البيانات")
            
    except Exception as e:
        print(f"خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_start_session()
