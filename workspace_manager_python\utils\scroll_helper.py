"""
مساعد السكرول - إضافة دعم السكرول بالماوس لجميع عناصر الواجهة
"""

import tkinter as tk
from tkinter import ttk


def bind_mousewheel_to_widget(widget, canvas=None):
    """
    ربط السكرول بالماوس لأي عنصر
    
    Args:
        widget: العنصر المراد إضافة السكرول له
        canvas: إذا كان العنصر داخل Canvas
    """
    
    def _on_mousewheel(event):
        """معالج حدث السكرول"""
        try:
            if canvas:
                # إذا كان العنصر داخل Canvas
                canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
            elif hasattr(widget, 'yview_scroll'):
                # إذا كان العنصر يدعم السكرول مباشرة
                widget.yview_scroll(int(-1 * (event.delta / 120)), "units")
            elif hasattr(widget, 'yview'):
                # إذا كان العنصر يدعم yview
                widget.yview_scroll(int(-1 * (event.delta / 120)), "units")
        except:
            pass
    
    def _bind_to_mousewheel(event):
        """ربط السكرول عند دخول الماوس"""
        widget.bind_all("<MouseWheel>", _on_mousewheel)
    
    def _unbind_from_mousewheel(event):
        """إلغاء ربط السكرول عند خروج الماوس"""
        widget.unbind_all("<MouseWheel>")
    
    # ربط الأحداث
    widget.bind('<Enter>', _bind_to_mousewheel)
    widget.bind('<Leave>', _unbind_from_mousewheel)


def make_scrollable_frame(parent, width=None, height=None):
    """
    إنشاء إطار قابل للسكرول
    
    Args:
        parent: العنصر الأب
        width: العرض (اختياري)
        height: الارتفاع (اختياري)
    
    Returns:
        tuple: (main_frame, scrollable_frame)
    """
    
    # الإطار الرئيسي
    main_frame = ttk.Frame(parent)
    
    # Canvas للسكرول
    canvas = tk.Canvas(main_frame, highlightthickness=0)
    
    # شريط التمرير العمودي
    v_scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    canvas.configure(yscrollcommand=v_scrollbar.set)
    
    # شريط التمرير الأفقي
    h_scrollbar = ttk.Scrollbar(main_frame, orient="horizontal", command=canvas.xview)
    canvas.configure(xscrollcommand=h_scrollbar.set)
    
    # الإطار القابل للسكرول
    scrollable_frame = ttk.Frame(canvas)
    
    # إضافة الإطار للـ Canvas
    canvas_frame = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    
    def _configure_scroll_region(event=None):
        """تحديث منطقة السكرول"""
        canvas.configure(scrollregion=canvas.bbox("all"))
    
    def _configure_canvas_size(event=None):
        """تحديث حجم Canvas"""
        canvas_width = canvas.winfo_width()
        canvas_height = canvas.winfo_height()
        
        # تحديث عرض الإطار ليملأ Canvas
        canvas.itemconfig(canvas_frame, width=canvas_width)
    
    # ربط الأحداث
    scrollable_frame.bind('<Configure>', _configure_scroll_region)
    canvas.bind('<Configure>', _configure_canvas_size)
    
    # إضافة دعم السكرول بالماوس
    bind_mousewheel_to_widget(canvas, canvas)
    bind_mousewheel_to_widget(scrollable_frame, canvas)
    
    # تخطيط العناصر
    canvas.pack(side="left", fill="both", expand=True)
    v_scrollbar.pack(side="right", fill="y")
    h_scrollbar.pack(side="bottom", fill="x")
    
    # تحديد الحجم إذا تم تمريره
    if width:
        canvas.configure(width=width)
    if height:
        canvas.configure(height=height)
    
    return main_frame, scrollable_frame


def add_scroll_to_treeview(treeview):
    """
    إضافة دعم السكرول لـ Treeview
    
    Args:
        treeview: عنصر Treeview
    """
    bind_mousewheel_to_widget(treeview)


def add_scroll_to_text(text_widget):
    """
    إضافة دعم السكرول لـ Text widget
    
    Args:
        text_widget: عنصر Text
    """
    bind_mousewheel_to_widget(text_widget)


def add_scroll_to_listbox(listbox):
    """
    إضافة دعم السكرول لـ Listbox
    
    Args:
        listbox: عنصر Listbox
    """
    bind_mousewheel_to_widget(listbox)


def add_scroll_to_combobox(combobox):
    """
    إضافة دعم السكرول لـ Combobox
    
    Args:
        combobox: عنصر Combobox
    """
    
    def _on_mousewheel(event):
        """معالج السكرول للـ Combobox"""
        try:
            current_index = combobox.current()
            values = combobox['values']
            
            if event.delta > 0:  # سكرول لأعلى
                new_index = max(0, current_index - 1)
            else:  # سكرول لأسفل
                new_index = min(len(values) - 1, current_index + 1)
            
            if 0 <= new_index < len(values):
                combobox.current(new_index)
                # إطلاق حدث التغيير
                combobox.event_generate('<<ComboboxSelected>>')
        except:
            pass
    
    def _bind_to_mousewheel(event):
        """ربط السكرول عند دخول الماوس"""
        combobox.bind_all("<MouseWheel>", _on_mousewheel)
    
    def _unbind_from_mousewheel(event):
        """إلغاء ربط السكرول عند خروج الماوس"""
        combobox.unbind_all("<MouseWheel>")
    
    # ربط الأحداث
    combobox.bind('<Enter>', _bind_to_mousewheel)
    combobox.bind('<Leave>', _unbind_from_mousewheel)


def add_scroll_to_frame(frame):
    """
    إضافة دعم السكرول لإطار عادي
    
    Args:
        frame: الإطار المراد إضافة السكرول له
    """
    
    def _on_mousewheel(event):
        """معالج السكرول للإطار"""
        try:
            # البحث عن أقرب عنصر قابل للسكرول
            parent = frame.winfo_parent()
            while parent:
                parent_widget = frame.nametowidget(parent)
                if hasattr(parent_widget, 'yview_scroll'):
                    parent_widget.yview_scroll(int(-1 * (event.delta / 120)), "units")
                    break
                parent = parent_widget.winfo_parent()
        except:
            pass
    
    def _bind_to_mousewheel(event):
        """ربط السكرول عند دخول الماوس"""
        frame.bind_all("<MouseWheel>", _on_mousewheel)
    
    def _unbind_from_mousewheel(event):
        """إلغاء ربط السكرول عند خروج الماوس"""
        frame.unbind_all("<MouseWheel>")
    
    # ربط الأحداث
    frame.bind('<Enter>', _bind_to_mousewheel)
    frame.bind('<Leave>', _unbind_from_mousewheel)


def enable_scroll_for_all_children(parent):
    """
    تفعيل السكرول لجميع العناصر الفرعية
    
    Args:
        parent: العنصر الأب
    """
    
    def _add_scroll_recursive(widget):
        """إضافة السكرول بشكل تكراري"""
        try:
            # تحديد نوع العنصر وإضافة السكرول المناسب
            widget_class = widget.winfo_class()
            
            if widget_class == 'Treeview':
                add_scroll_to_treeview(widget)
            elif widget_class == 'Text':
                add_scroll_to_text(widget)
            elif widget_class == 'Listbox':
                add_scroll_to_listbox(widget)
            elif widget_class == 'TCombobox':
                add_scroll_to_combobox(widget)
            elif widget_class in ['Frame', 'TFrame', 'LabelFrame', 'TLabelFrame']:
                add_scroll_to_frame(widget)
            
            # تطبيق على العناصر الفرعية
            for child in widget.winfo_children():
                _add_scroll_recursive(child)
                
        except Exception as e:
            # تجاهل الأخطاء والمتابعة
            pass
    
    _add_scroll_recursive(parent)


# دالة سهلة للاستخدام
def make_everything_scrollable(root_window):
    """
    جعل كل شيء في النافذة قابل للسكرول
    
    Args:
        root_window: النافذة الرئيسية أو أي عنصر أب
    """
    enable_scroll_for_all_children(root_window)
    
    # إضافة السكرول للنافذة نفسها إذا أمكن
    try:
        bind_mousewheel_to_widget(root_window)
    except:
        pass
